# Smart Pump WebSocket Implementation

This document describes the WebSocket implementation for real-time Smart Pump data updates, including nozzle details and daily sales aggregation.

## Overview

The WebSocket implementation provides real-time updates for:

-   **Nozzle Dashboard Latest Pump Nozzles Detail** - Real-time pump and nozzle basic information (dashboard view)
-   **Nozzle Details** - Detailed real-time data for specific nozzles including transaction details, flow rates, and status
-   **Nozzle Dashboard Daily Sales Aggregation By Product** - Real-time sales data aggregated by product type
-   **Transaction Logs** - Real-time transaction data from nozzle operations

## WebSocket Endpoints

### 1. All Smart Pump Data

```
ws://your-domain/ws/smart-pump/{site_id}/
```

Receives all types of smart pump updates for the specified site.

### 2. Nozzle Data Only

```
ws://your-domain/ws/smart-pump/{site_id}/nozzles/
```

Receives only nozzle-related updates for the specified site.

### 3. Sales Data Only

```
ws://your-domain/ws/smart-pump/{site_id}/sales/
```

Receives only sales aggregation updates for the specified site.

### 4. Transaction Logs Only

```
ws://your-domain/ws/smart-pump/{site_id}/transactions/
```

Receives only transaction log updates for the specified site.

## Message Format

All WebSocket messages follow this JSON structure:

```json
{
  "type": "message_type",
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### Message Types

#### 1. Nozzle Updates (`nozzle_update`)

**Dashboard Data (Basic Info):**

```json
{
    "type": "nozzle_update",
    "data": {
        "data": [
            {
                "nozzle_address": "1:123",
                "nozzle_count": 4,
                "pump_id": 123,
                "nozzle_name": "Nozzle 1",
                "site_name": "Main Station",
                "pump_name": "Pump 1",
                "pump_brand": "Wayne",
                "product": "PMS",
                "price_per_unit": 650.0,
                "total_volume": "0.000",
                "total_value": "0.000"
            }
        ],
        "currency_symbol": "₦"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Detailed Nozzle Data:**

```json
{
    "type": "nozzle_update",
    "data": {
        "nozzle_address": "1",
        "mac_address": "AA:BB:CC:DD:EE:FF",
        "device_id": "456",
        "details": {
            "calculated_flow_rate": 25.5,
            "totalizer": 12345.678,
            "raw_volume": 25.5,
            "transaction_time": "2024-01-01T12:00:00",
            "status": "online",
            "status_description": "The pump is connected and actively communicating",
            "last_updated_time": "2024-01-01T12:00:00"
        },
        "timestamp": "2024-01-01T12:00:00.000Z"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 2. Sales Updates (`sales_update`)

```json
{
    "type": "sales_update",
    "data": {
        "pms_total_sales": "1,234.567",
        "pms_total_volume": "987.654",
        "ago_total_sales": "2,345.678",
        "ago_total_volume": "1,876.543",
        "dpk_total_sales": "3,456.789",
        "dpk_total_volume": "2,765.432",
        "cng_total_sales": "4,567.890",
        "cng_total_volume": "3,654.321",
        "ago_sales": true,
        "pms_sales": true,
        "cng_sales": false,
        "dpk_sales": true,
        "currency_symbol": "₦"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### 3. Transaction Updates (`transaction_update`)

```json
{
    "type": "transaction_update",
    "data": {
        "transaction_id": 12345,
        "local_id": "TXN_001",
        "nozzle_address": "1",
        "raw_nozzle_address": "01",
        "transaction_start_time": "2024-01-01T12:00:00",
        "transaction_stop_time": "2024-01-01T12:05:00",
        "transaction_raw_volume": 25.5,
        "transaction_raw_amount": 16575.0,
        "raw_transaction_price_per_unit": 650.0,
        "computed_total": 16575.0,
        "pump_mac_address": "AA:BB:CC:DD:EE:FF",
        "pump_name": "Pump 1",
        "product_name": "PMS",
        "site_id": 123,
        "site_name": "Main Station",
        "device_id": 456,
        "uploaded_time": "2024-01-01T12:05:30",
        "user_id": "ATT001",
        "status": "Completed",
        "totalizer_start_volume": 1000.0,
        "totalizer_stop_volume": 1025.5,
        "totalizer_start_amount": 650000.0,
        "totalizer_stop_amount": 666575.0
    },
    "timestamp": "2024-01-01T12:05:30.000Z"
}
```

#### 4. General Smart Pump Updates (`smart_pump_update`)

```json
{
  "type": "smart_pump_update",
  "data": {
    "update_type": "nozzles|sales|transaction|general",
    "payload": { ... }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Client Implementation

### JavaScript Example

```javascript
// Connect to WebSocket
const socket = new WebSocket('ws://localhost:8000/ws/smart-pump/123/');

socket.onopen = function (event) {
    console.log('Connected to Smart Pump WebSocket');
};

socket.onmessage = function (event) {
    const data = JSON.parse(event.data);

    switch (data.type) {
        case 'nozzle_update':
            updateNozzleDisplay(data.data);
            break;
        case 'sales_update':
            updateSalesDisplay(data.data);
            break;
        case 'transaction_update':
            updateTransactionDisplay(data.data);
            break;
        case 'smart_pump_update':
            handleGeneralUpdate(data.data);
            break;
    }
};

socket.onclose = function (event) {
    console.log('WebSocket connection closed');
};

socket.onerror = function (error) {
    console.error('WebSocket error:', error);
};
```

### Python Example

```python
import asyncio
import websockets
import json

async def smart_pump_client():
    uri = "ws://localhost:8000/ws/smart-pump/123/"

    async with websockets.connect(uri) as websocket:
        async for message in websocket:
            data = json.loads(message)
            print(f"Received {data['type']}: {data['data']}")

# Run the client
asyncio.run(smart_pump_client())
```

## Backend Integration

### Triggering Updates from Views

```python
from backend.smart_pump.consumers import push_nozzle_update, push_sales_update

# In your view or service
def update_nozzle_data(site_id):
    # Get your data
    nozzle_data = get_nozzle_data(site_id)

    # Push to WebSocket clients
    push_nozzle_update(site_id, nozzle_data)

def update_sales_data(site_id):
    # Get your data
    sales_data = get_sales_data(site_id)

    # Push to WebSocket clients
    push_sales_update(site_id, sales_data)
```

### Using Celery Tasks

```python
from backend.smart_pump.tasks import (
    push_smart_pump_websocket_updates,
    push_latest_transactions_websocket,
    push_nozzle_details_websocket
)

# Trigger dashboard updates for a specific site
push_smart_pump_websocket_updates.delay(site_id)

# Push detailed nozzle data for a specific nozzle
push_nozzle_details_websocket.delay(site_id, mac_address, nozzle_address, device_id)

# Push latest transaction logs for a site
push_latest_transactions_websocket.delay(site_id, limit=10)

# Trigger updates for all sites
from backend.smart_pump.tasks import push_smart_pump_websocket_updates_all_sites
push_smart_pump_websocket_updates_all_sites.delay()
```

### Automatic Transaction Updates

Transaction updates are automatically pushed via Django signals whenever a new `TransactionData` record is created. This happens when:

-   New transactions are logged via `TransactionLogger` endpoint
-   New transactions are logged via `PicTransactionLogger` endpoint
-   Any direct creation of `TransactionData` model instances

## Testing

### 1. Using the Test Script

```bash
cd backend/smart_pump
python test_websocket.py 123
```

### 2. Using the HTML Test Client

Open `websocket_test_client.html` in your browser and:

1. Enter a site ID
2. Click "Connect"
3. Monitor real-time messages

### 3. Manual Testing with curl

```bash
# Test the REST endpoints first
curl "http://localhost:8000/api/v1/smartpump/pump_dashboard_latest_pump_details/?Site_id=123"
curl "http://localhost:8000/api/v1/smartpump/pump_dashboard_daily_sales_aggregation_by_products/?Site_id=123"
```

## Configuration

### Redis Configuration

Ensure Redis is properly configured in your settings:

```python
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [("127.0.0.1", 6379)],
        },
    },
}
```

### Celery Configuration

The following tasks are configured for WebSocket updates:

-   `push_smart_pump_websocket_updates`
-   `push_smart_pump_websocket_updates_all_sites`

## Deployment Considerations

1. **ASGI Server**: Use an ASGI server like Daphne or Uvicorn for WebSocket support
2. **Redis**: Ensure Redis is available for channel layer communication
3. **Celery**: Configure Celery workers to handle background tasks
4. **Load Balancing**: Consider sticky sessions for WebSocket connections

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if the ASGI server is running
2. **No Messages**: Verify Redis connection and Celery workers
3. **Authentication**: Ensure proper authentication middleware if required
4. **CORS**: Configure CORS settings for cross-origin WebSocket connections

### Debug Mode

Enable debug logging in your Django settings:

```python
LOGGING = {
    'loggers': {
        'backend.smart_pump': {
            'level': 'DEBUG',
        },
    },
}
```

## Security Considerations

1. **Authentication**: Implement proper authentication for WebSocket connections
2. **Authorization**: Verify user permissions for site data access
3. **Rate Limiting**: Implement rate limiting to prevent abuse
4. **Data Validation**: Validate all incoming data before broadcasting

## Performance Tips

1. **Connection Pooling**: Use connection pooling for database queries
2. **Caching**: Implement caching for frequently accessed data
3. **Batch Updates**: Batch multiple updates when possible
4. **Monitoring**: Monitor WebSocket connection counts and message rates
