from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from channels.generic.websocket import AsyncWebsocketConsumer
import json
from datetime import datetime


class SmartPumpConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.site_id = self.scope['url_route']['kwargs']['site_id']
        self.data_type = self.scope['url_route']['kwargs'].get('data_type', 'all')
        
        # Create group names for different data types
        if self.data_type == 'nozzles':
            self.group_name = f"smart_pump_nozzles_{self.site_id}"
        elif self.data_type == 'sales':
            self.group_name = f"smart_pump_sales_{self.site_id}"
        else:
            # Default to all data types
            self.group_name = f"smart_pump_all_{self.site_id}"

        # Join site-specific group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )

    # Handle nozzle data updates
    async def send_nozzle_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "nozzle_update",
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))

    # Handle sales aggregation updates
    async def send_sales_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "sales_update", 
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))

    # Handle general smart pump updates
    async def send_smart_pump_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "smart_pump_update",
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))


def push_nozzle_update(site_id, nozzle_data):
    """
    Push real-time nozzle data updates to WebSocket clients
    site_id: int - The site ID
    nozzle_data: dict - The nozzle data payload
    """
    channel_layer = get_channel_layer()
    
    # Send to nozzle-specific group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_nozzles_{site_id}",
        {
            "type": "send_nozzle_update",
            "data": nozzle_data,
        }
    )
    
    # Also send to general smart pump group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": "nozzles",
                "payload": nozzle_data
            },
        }
    )


def push_sales_update(site_id, sales_data):
    """
    Push real-time sales aggregation updates to WebSocket clients
    site_id: int - The site ID
    sales_data: dict - The sales aggregation data payload
    """
    channel_layer = get_channel_layer()
    
    # Send to sales-specific group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_sales_{site_id}",
        {
            "type": "send_sales_update",
            "data": sales_data,
        }
    )
    
    # Also send to general smart pump group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": "sales",
                "payload": sales_data
            },
        }
    )


def push_smart_pump_update(site_id, update_data, update_type="general"):
    """
    Push general smart pump updates to WebSocket clients
    site_id: int - The site ID
    update_data: dict - The update data payload
    update_type: str - Type of update (general, nozzles, sales, etc.)
    """
    channel_layer = get_channel_layer()
    
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": update_type,
                "payload": update_data,
                "timestamp": datetime.now().isoformat()
            },
        }
    )
