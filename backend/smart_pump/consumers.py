from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from channels.generic.websocket import AsyncWebsocketConsumer
import json
from datetime import datetime
from decimal import Decimal


class SmartPumpConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.site_id = self.scope['url_route']['kwargs']['site_id']
        self.data_type = self.scope['url_route']['kwargs'].get(
            'data_type', 'all')

        # Create group names for different data types
        if self.data_type == 'nozzles':
            self.group_name = f"smart_pump_nozzles_{self.site_id}"
        elif self.data_type == 'sales':
            self.group_name = f"smart_pump_sales_{self.site_id}"
        elif self.data_type == 'transactions':
            self.group_name = f"smart_pump_transactions_{self.site_id}"
        else:
            # Default to all data types
            self.group_name = f"smart_pump_all_{self.site_id}"

        # Join site-specific group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )

    # Handle nozzle data updates
    async def send_nozzle_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "nozzle_update",
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))

    # Handle sales aggregation updates
    async def send_sales_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "sales_update",
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))

    # Handle general smart pump updates
    async def send_smart_pump_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "smart_pump_update",
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))

    # Handle transaction log updates
    async def send_transaction_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "transaction_update",
            "data": event["data"],
            "timestamp": datetime.now().isoformat()
        }))


def push_nozzle_update(site_id, nozzle_data):
    """
    Push real-time nozzle data updates to WebSocket clients
    site_id: int - The site ID
    nozzle_data: dict - The nozzle data payload
    """
    channel_layer = get_channel_layer()

    # Determine update type based on data structure
    update_subtype = "dashboard"  # Default for dashboard data
    if "nozzle_address" in nozzle_data and "details" in nozzle_data:
        update_subtype = "details"  # Specific nozzle details
    elif "data" in nozzle_data and isinstance(nozzle_data["data"], list):
        update_subtype = "dashboard"  # Dashboard list data

    # Send to nozzle-specific group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_nozzles_{site_id}",
        {
            "type": "send_nozzle_update",
            "data": nozzle_data,
        }
    )

    # Also send to general smart pump group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": "nozzles",
                "update_subtype": update_subtype,
                "payload": nozzle_data
            },
        }
    )


def push_sales_update(site_id, sales_data):
    """
    Push real-time sales aggregation updates to WebSocket clients
    site_id: int - The site ID
    sales_data: dict - The sales aggregation data payload
    """
    channel_layer = get_channel_layer()

    # Send to sales-specific group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_sales_{site_id}",
        {
            "type": "send_sales_update",
            "data": sales_data,
        }
    )

    # Also send to general smart pump group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": "sales",
                "payload": sales_data
            },
        }
    )


def format_transaction_data(transaction_instance):
    """
    Format TransactionData instance for WebSocket transmission
    """
    try:
        return {
            "transaction_id": transaction_instance.Transaction_id,
            "local_id": transaction_instance.local_id,
            "nozzle_address": transaction_instance.Nozzle_address,
            "raw_nozzle_address": transaction_instance.Raw_nozzle_address,
            "transaction_start_time": transaction_instance.Transaction_start_time.isoformat() if transaction_instance.Transaction_start_time else None,
            "transaction_stop_time": transaction_instance.Transaction_stop_time.isoformat() if transaction_instance.Transaction_stop_time else None,
            "transaction_raw_volume": float(transaction_instance.Transaction_raw_volume),
            "transaction_raw_amount": float(transaction_instance.Transaction_raw_amount),
            "raw_transaction_price_per_unit": float(transaction_instance.Raw_transaction_price_per_unit),
            "computed_total": float(transaction_instance.computed_total) if transaction_instance.computed_total else None,
            "pump_mac_address": transaction_instance.Pump_mac_address,
            "pump_name": transaction_instance.pump_name,
            "product_name": transaction_instance.Product_name,
            "site_id": transaction_instance.Site.Site_id if transaction_instance.Site else None,
            "site_name": transaction_instance.Site.Name if transaction_instance.Site else None,
            "device_id": transaction_instance.Device.Device_id if transaction_instance.Device else None,
            "uploaded_time": transaction_instance.Uploaded_time.isoformat() if transaction_instance.Uploaded_time else None,
            "user_id": transaction_instance.user_id,
            "status": transaction_instance.Status,
            "totalizer_start_volume": float(transaction_instance.Transaction_start_pump_totalizer_volume) if transaction_instance.Transaction_start_pump_totalizer_volume else None,
            "totalizer_stop_volume": float(transaction_instance.Transaction_stop_pump_totalizer_volume) if transaction_instance.Transaction_stop_pump_totalizer_volume else None,
            "totalizer_start_amount": float(transaction_instance.Transaction_start_pump_totalizer_amount) if transaction_instance.Transaction_start_pump_totalizer_amount else None,
            "totalizer_stop_amount": float(transaction_instance.Transaction_stop_pump_totalizer_amount) if transaction_instance.Transaction_stop_pump_totalizer_amount else None,
        }
    except Exception as e:
        # Return basic data if formatting fails
        return {
            "transaction_id": getattr(transaction_instance, 'Transaction_id', None),
            "local_id": getattr(transaction_instance, 'local_id', None),
            "error": f"Formatting error: {str(e)}"
        }


def push_transaction_update(site_id, transaction_data):
    """
    Push real-time transaction log updates to WebSocket clients
    site_id: int - The site ID
    transaction_data: dict - The transaction data payload (formatted)
    """
    channel_layer = get_channel_layer()

    # Send to transaction-specific group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_transactions_{site_id}",
        {
            "type": "send_transaction_update",
            "data": transaction_data,
        }
    )

    # Also send to general smart pump group
    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": "transaction",
                "payload": transaction_data
            },
        }
    )


def push_smart_pump_update(site_id, update_data, update_type="general"):
    """
    Push general smart pump updates to WebSocket clients
    site_id: int - The site ID
    update_data: dict - The update data payload
    update_type: str - Type of update (general, nozzles, sales, etc.)
    """
    channel_layer = get_channel_layer()

    async_to_sync(channel_layer.group_send)(
        f"smart_pump_all_{site_id}",
        {
            "type": "send_smart_pump_update",
            "data": {
                "update_type": update_type,
                "payload": update_data,
                "timestamp": datetime.now().isoformat()
            },
        }
    )
