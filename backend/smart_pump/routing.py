from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # WebSocket route for all smart pump data for a specific site
    re_path(r"ws/smart-pump/(?P<site_id>\d+)/$",
            consumers.SmartPumpConsumer.as_asgi()),

    # WebSocket route specifically for nozzle data
    re_path(r"ws/smart-pump/(?P<site_id>\d+)/nozzles/$",
            consumers.SmartPumpConsumer.as_asgi(), {'data_type': 'nozzles'}),

    # WebSocket route specifically for sales aggregation data
    re_path(r"ws/smart-pump/(?P<site_id>\d+)/sales/$",
            consumers.SmartPumpConsumer.as_asgi(), {'data_type': 'sales'}),

    # WebSocket route specifically for transaction logs
    re_path(r"ws/smart-pump/(?P<site_id>\d+)/transactions/$",
            consumers.SmartPumpConsumer.as_asgi(), {'data_type': 'transactions'}),
]
