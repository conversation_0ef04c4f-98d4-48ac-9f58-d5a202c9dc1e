<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Pump WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .message {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .message.nozzle {
            border-left: 4px solid #007bff;
        }
        .message.sales {
            border-left: 4px solid #28a745;
        }
        .message.error {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        .messages-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Smart Pump WebSocket Test Client</h1>
        
        <div class="controls">
            <label>Site ID:</label>
            <input type="text" id="siteId" value="123" placeholder="Enter site ID">
            
            <label>WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8000/ws/smart-pump/" placeholder="WebSocket base URL">
            
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearMessages()">Clear Messages</button>
        </div>
        
        <div id="status" class="status disconnected">Disconnected</div>
    </div>

    <div class="container">
        <h2>📊 Real-time Data</h2>
        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-value" id="messageCount">0</div>
                <div class="stat-label">Messages Received</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="nozzleCount">0</div>
                <div class="stat-label">Nozzle Updates</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="salesCount">0</div>
                <div class="stat-label">Sales Updates</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="lastUpdate">Never</div>
                <div class="stat-label">Last Update</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📨 Messages</h2>
        <div class="messages-container" id="messagesContainer">
            <div class="message">Waiting for connection...</div>
        </div>
    </div>

    <script>
        let socket = null;
        let messageCount = 0;
        let nozzleCount = 0;
        let salesCount = 0;

        function updateStatus(message, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${className}`;
        }

        function addMessage(message, type = 'info') {
            const container = document.getElementById('messagesContainer');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(messageEl);
            container.scrollTop = container.scrollHeight;
            
            messageCount++;
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        function updateStats(data) {
            if (data.type === 'nozzle_update') {
                nozzleCount++;
                document.getElementById('nozzleCount').textContent = nozzleCount;
            } else if (data.type === 'sales_update') {
                salesCount++;
                document.getElementById('salesCount').textContent = salesCount;
            }
        }

        function connect() {
            const siteId = document.getElementById('siteId').value;
            const wsUrl = document.getElementById('wsUrl').value;
            
            if (!siteId) {
                alert('Please enter a site ID');
                return;
            }

            const fullUrl = `${wsUrl}${siteId}/`;
            
            updateStatus('Connecting...', 'connecting');
            addMessage(`Connecting to ${fullUrl}`, 'info');

            socket = new WebSocket(fullUrl);

            socket.onopen = function(event) {
                updateStatus(`Connected to site ${siteId}`, 'connected');
                addMessage('WebSocket connection established', 'info');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            };

            socket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage(`Received ${data.type}: ${JSON.stringify(data.data, null, 2)}`, data.type.replace('_update', ''));
                    updateStats(data);
                } catch (e) {
                    addMessage(`Raw message: ${event.data}`, 'info');
                }
            };

            socket.onclose = function(event) {
                updateStatus('Disconnected', 'disconnected');
                addMessage(`Connection closed (code: ${event.code})`, 'error');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            };

            socket.onerror = function(error) {
                updateStatus('Connection Error', 'disconnected');
                addMessage(`WebSocket error: ${error}`, 'error');
            };
        }

        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
            }
        }

        function clearMessages() {
            document.getElementById('messagesContainer').innerHTML = '<div class="message">Messages cleared...</div>';
            messageCount = 0;
            nozzleCount = 0;
            salesCount = 0;
            document.getElementById('messageCount').textContent = '0';
            document.getElementById('nozzleCount').textContent = '0';
            document.getElementById('salesCount').textContent = '0';
            document.getElementById('lastUpdate').textContent = 'Never';
        }

        // Auto-connect on page load if site ID is provided
        window.onload = function() {
            const siteId = document.getElementById('siteId').value;
            if (siteId) {
                // Uncomment the line below to auto-connect
                // connect();
            }
        };
    </script>
</body>
</html>
