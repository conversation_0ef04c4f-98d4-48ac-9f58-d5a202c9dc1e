import asyncio
from collections import OrderedDict
import threading
from unicodedata import decimal
import json
from rest_framework.views import APIView
from backend.currency.utils import get_currency
from rest_framework import generics
from backend.devices.utils import get_device_id_by_mac_address, get_device_mac_address, get_device_name_by_mac_address, get_pump_name_by_device_id, get_pump_site_by_device_id
from backend.sites.utils import get_site_name_by_siteid
from backend.smart_pump.tasks import save_smart_pump_dashboard_data_to_redis
from backend.smarteye_logs.queries import update_device_online_status
from . import serializer
from .serializer import TransactionDataResponseSerializer
from .. import utils
from rest_framework import status
from backend import models
from backend.smart_pump.utils import send_price_change_notification, send_remote_config
from .. import permissions
from . import utils as u
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import date, datetime
import operator
from django.core.cache import cache as django_cache
from decouple import config
from asgiref.sync import sync_to_async
from backend.tasks import send_sales_summary_report, send_price_change_notification_task, send_sales_summary_new
from backend.smart_pump.queries import get_ago_price_per_unit, get_ago_total_volume, get_all_pump_mode_status, get_dpk_price_per_unit, get_dpk_total_volume, get_pms_price_per_unit, get_pms_total_volume, get_products_by_site_id, get_pump_mode_status, get_remote_configs, insert_pump_mode_switch_log,  update_status, get_pumps, get_nozzle, get_product, get_cng_total_volume, new_summary, new_summary_with_product, new_summary_with_nozzle, new_summary_with_product_and_nozzle
from django.db import IntegrityError, connection
from django.db.models import Q, F
from django.shortcuts import get_object_or_404
from django.db import transaction
from rest_framework.response import Response
from backend.custom_pagination import HeaderLimitOffsetPagination, LargeResultsSetPagination, SmallResultsSetPagination
from .. import permissions
from backend.smart_pump import cache
from decimal import Decimal, ROUND_HALF_UP
redis_client = cache.RedisClient()
executor = ThreadPoolExecutor(max_workers=5)


class TransactionSummary(APIView):
    serializer_class = serializer.TransactionDataSerializer

    def get(self, request):

        # check if there is NozzleName on the request and Product Name
        if len(request.GET['NozzleName']) > 0 and len(request.GET['Product']) > 0:
            _resp = new_summary_with_product_and_nozzle(
                request.GET['Site_id'], request.GET['Start_time'], request.GET['End_time'], request.GET['NozzleName'], request.GET['Product'])
        #  Check if the request has nozzle Name and no Product Name
        elif request.GET['NozzleName'] == '' and request.GET['Product'] != '':
            _resp = new_summary_with_product(
                request.GET['Site_id'], request.GET['Start_time'], request.GET['End_time'], request.GET['Product'])
        elif request.GET['NozzleName'] != '' and request.GET['Product'] == '':
            _resp = new_summary_with_nozzle(
                request.GET['Site_id'], request.GET['Start_time'], request.GET['End_time'], request.GET['NozzleName'])
        else:
            _resp = new_summary(
                request.GET['Site_id'], request.GET['Start_time'], request.GET['End_time'])

        # _resp = new_summary2(request.GET['Site_id'],request.GET['Start_time'],request.GET['End_time'], request.GET['NozzleName'])
        # convert to dictionary
        final_response = []
        for resp in _resp:
            total_value = Decimal(str(resp[0])).quantize(
                Decimal("0.001"), rounding=ROUND_HALF_UP)
            total_volume = Decimal(str(resp[6])).quantize(
                Decimal("0.001"), rounding=ROUND_HALF_UP)

            respDict = {
                "date": resp[2],
                "product": resp[1],
                "nozzle": resp[4],
                "total_volume": f'{total_volume:,.3f}',
                "total_value(\u20a6)": f'{total_value:,.3f}',
                "site": resp[3],
                "pump_name": resp[5]
            }
            final_response.append(respDict)

        Site_id = request.GET['Site_id']
        # # get date ranges
        dateRange = u.getDateRange(request)
        # check if to delay report
        delayThisReport = u.delaySendingReportStatus(dateRange, config(
            'REPORT_DELAY_MAX_DAYS', cast=int), config('REPORT_DELAY_ACTIVE', cast=bool))
        if delayThisReport:
            # send task to celery
            email_receiver = (request.user.Email)
            # string format here else Json unserializable
            mail_receiver_name = (f'{request.user}')
            # u.send_sales_summary_report(email_receiver, f'{mail_receiver_name}',dateRange,Site_id, passed_products)
            send_sales_summary_new.delay(
                request.GET['Site_id'], request.GET['Start_time'], request.GET['End_time'], mail_receiver_name, email_receiver)
            return utils.UpdatedCustomResponse.Success({"message": "Generated Report will be sent to your registered email address!"}, status=status.HTTP_200_OK)
        return utils.UpdatedCustomResponse.Success(final_response, status=status.HTTP_200_OK)


class ProductInSIte(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, site_id):

        products = []
        # product - > nozzle -> pump -> site
        pump = models.Pump.objects.filter(Site_id=site_id)
        for x in pump:
            nozzle = models.Nozzle.objects.filter(Pump_id=x.id)
            for y in nozzle:
                _ = {
                    'name': y.Product.Name
                }
                products.append(_)
        resp = list({v['name']: v for v in products}.values())
        return utils.CustomResponse.Success(resp)


class NozzleInSIte(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, site_id):
        nozzles = models.Nozzle.objects.filter(Pump__Site_id=site_id).select_related("Product", "Pump").values(
            "id","Name", "Nozzle_address", "Product__Name", "Pump__Name", "Pump__id"
        )
        serializers = serializer.NozzleListSiteSerializer(nozzles, many=True)
        return utils.CustomResponse.Success(serializers.data)


class NozzleData(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, device_id, nozzle_id):
        # from device -> pump -> nozzles
        device = models.Devices.objects.get(Device_unique_address=device_id)
        pump = models.Pump.objects.get(Device_id=device.Device_id)
        nozzle = models.Nozzle.objects.get(
            Pump_id=pump.id, Nozzle_address=nozzle_id)
        serilizer = serializer.NozSerializer(nozzle)
        return utils.CustomResponse.Success(serilizer.data, status=200)


class PicTransactionLoggerV2(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, *args, **kwargs):
        id = request.GET['id']
        na = request.GET['na']
        stattime = request.GET['stattime']
        stoptime = request.GET['stoptime']
        rv = request.GET['rv']
        ra = request.GET['ra']
        ppu = request.GET['ppu']
        pma = request.GET['pma']
        tstatv = request.GET['tstatv']
        tstoptv = request.GET['tstoptv']
        tstata = request.GET['tstata']
        tstopta = request.GET['tstopta']
        de = request.GET['de']
        site = request.GET['site']

        count_log_fail = 0
        error = "No errors"
        this_device = get_object_or_404(models.Devices, pk=de)
        this_site = get_object_or_404(models.Sites, pk=site)

        models.TransactionData.objects.create(local_id=id, Nozzle_address=na, Transaction_start_time=stattime, Transaction_stop_time=stoptime,
                                              Transaction_raw_volume=rv, Transaction_raw_amount=ra,
                                              Raw_transaction_price_per_unit=ppu, Pump_mac_address=pma,
                                              Transaction_start_pump_totalizer_volume=tstatv, Transaction_stop_pump_totalizer_volume=tstoptv,
                                              Transaction_start_pump_totalizer_amount=tstata, Transaction_stop_pump_totalizer_amount=tstopta,
                                              Device=this_device, Site=this_site
                                              )

        return utils.CustomResponse.Success('success', status=status.HTTP_200_OK)


class PicTransactionConfirmation(APIView):
    permission_classes = ()
    authentication_classes = ()

    def get(self, request):
        id = request.GET['id']

        if models.TransactionData.objects.filter(local_id=id).exists():
            return Response('y', status=status.HTTP_201_CREATED)
        return Response('n', status=status.HTTP_201_CREATED)


class PicTransactionLogger(APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request, *args, **kwargs):
        count_log_fail = 0
        count_log_saved = 0
        error = "No errors"
        message = "Creation was successful"
        shift_user_id = ""
        site_id = request.data[0][13]
        get_user_on_shift = models.ShiftUserControl.objects.filter(
            Site_id=site_id, login_status='start').values("user_id")
        if get_user_on_shift:
            shift_user_id = get_user_on_shift[0]['user_id']

        transaction_data = request.data
        try:

            for items in transaction_data:
                '''
                index 0 - transaction_id
                index 1 - Nozzle_address
                index 2 - start_time
                index 3 - end_time
                index 4 - raw_volume
                index 5 - raw_amount
                index 6 - raw_ppu
                index 7 - mac_address
                index 8 - volume_Start_totalizer
                index 9 - volume_end_totalizer
                index 10 - amount_Start_totalizer
                index 11 - amount_end_totalizer
                index 12 - Device
                index 13 - Site or site id
                index 14 - local_id / uniques identifier 
                '''
                this_device = get_object_or_404(models.Devices, pk=items[12])
                this_site = get_object_or_404(models.Sites, pk=items[13])
                # convert hexadecimal to decimal / whole number
                nozzle_add = int(items[1], 16)
                decimal_config = models.Nozzle.objects.filter(
                    Pump__Device__Device_unique_address=items[7]).values()
                # Check if decimal formatting is needed
                check_pump_decimal_config = models.Pump.objects.filter(
                    id=decimal_config[0]['Pump_id']).values("use_decimal_config")
                _decimal_config = check_pump_decimal_config[0]['use_decimal_config']
                if not _decimal_config:
                    raw_volume = int(
                        items[4]) / (10**decimal_config[0]['Decimal_setting_volume'])
                    raw_amount = int(
                        items[5]) / (10**decimal_config[0]['Decimal_setting_amount'])
                    raw_upp = int(items[6]) / (10**decimal_config[0]
                                               ['Decimal_setting_price_unit'])
                    calculated_total = (raw_volume * raw_upp)
                else:
                    raw_volume = items[4]
                    raw_amount = items[5]
                    raw_upp = items[6]
                    calculated_total = (float(raw_volume) * float(raw_upp))
                    transaction_data = {
                        "local_id": items[14],
                        "Nozzle_address": nozzle_add,
                        "Transaction_start_time": items[2],
                        "Transaction_stop_time": items[3],
                        "Transaction_raw_volume": str(raw_volume),
                        "Transaction_raw_amount": str(raw_amount),
                        "Raw_transaction_price_per_unit": str(raw_upp),
                        "computed_total": calculated_total,
                        "Pump_mac_address": items[7],
                        "Transaction_start_pump_totalizer_volume": items[8],
                        "Transaction_stop_pump_totalizer_volume": items[9],
                        "Transaction_start_pump_totalizer_amount": items[10],
                        "Transaction_stop_pump_totalizer_amount": items[11],
                        "Device": items[12],
                        "Site": items[13],
                        "Raw_nozzle_address": items[1],
                        "user_id": shift_user_id
                    }

                try:
                    with transaction.atomic():
                        models.TransactionData.objects.create(
                            local_id=items[14],
                            Nozzle_address=nozzle_add,
                            Transaction_start_time=str(items[2]),
                            Transaction_stop_time=str(items[3]),
                            Transaction_raw_volume=str(raw_volume),
                            Transaction_raw_amount=str(raw_amount),
                            Raw_transaction_price_per_unit=str(raw_upp),
                            computed_total=calculated_total,
                            Pump_mac_address=items[7],
                            Transaction_start_pump_totalizer_volume=items[8],
                            Transaction_stop_pump_totalizer_volume=items[9],
                            Transaction_start_pump_totalizer_amount=items[10],
                            Transaction_stop_pump_totalizer_amount=items[11],
                            Device=this_device,
                            Site=this_site,
                            Raw_nozzle_address=items[1],
                            user_id=shift_user_id
                        )
                        count_log_saved += 1

                        # Trigger task in background
                        executor.submit(
                            save_smart_pump_dashboard_data_to_redis, items[13])

                except IntegrityError as e:
                    count_log_fail += 1
                    continue

            # type: ignore
            return utils.CustomResponse.Success(f"{count_log_fail} log(s) exist and {count_log_saved} log(s)saved successfully", status=status.HTTP_200_OK)
        except:
            # type: ignore
            return utils.CustomResponse.Failure("Error creating transaction", status=status.HTTP_400_BAD_REQUEST)


class UpdatePriceChange(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        data = request.data
        device_id, new_price = data.get('device_id'), data.get('new_price')

        current_date = datetime.now()
        todays_time = current_date.strftime("%Y-%m-%d %H:%M:%S")

        pump = get_object_or_404(models.Pump, Device_id=device_id)
        site_id = pump.Site.Site_id

        count_pump_in_site = models.Pump.objects.filter(Site=site_id).count()

        try:
            price_data = models.PumpPriceChangeRecord.objects.filter(
                site_id=site_id, new_price=new_price).last()

            if price_data:
                if (price_data.pump_count < price_data.total_pump_in_site) and (price_data.total_pump_in_site - price_data.pump_count > 1):
                    price_data.pump_count += 1
                    price_data.pump_data['device_id'].append(
                        pump.Device.Device_id)
                    price_data.pump_data['mac_address'].append(
                        pump.Device.Device_unique_address)
                    price_data.save()
                    return utils.CustomResponse.Success("Status Updated Successfully", status=status.HTTP_200_OK)
                else:
                    price_data.delete()
                    price_status = models.PriceChangeRequestData.objects.get(
                        Site=site_id, New_price=new_price, Approved=True)
                    price_status.pump_price_change_status = "Fully Effected"
                    price_status.save()
                    return utils.CustomResponse.Success("Status Updated Successfully", status=status.HTTP_200_OK)
            else:
                obj, created = models.PumpPriceChangeRecord.objects.get_or_create(
                    site_id=site_id,
                    total_pump_in_site=count_pump_in_site,
                    new_price=new_price,
                    pump_data={
                        'device_id': [pump.Device.Device_id],
                        'mac_address': [pump.Device.Device_unique_address]
                    },
                    time_added=todays_time
                )
                obj.pump_count += 1
                obj.save()
                price_status = models.PriceChangeRequestData.objects.filter(
                    Site=site_id,
                    New_price=new_price,
                    Approved=True
                ).last()
                if price_status:
                    if obj.total_pump_in_site == obj.pump_count:
                        price_status.pump_price_change_status = "Fully Effected"
                        price_status.save()
                        obj.delete()
                    else:
                        price_status.pump_price_change_status = "Partially Effected"
                        price_status.save()
                else:
                    return utils.CustomResponse.Failure("Record Not Found", status=status.HTTP_400_BAD_REQUEST)
                return utils.CustomResponse.Success("Status Received", status=status.HTTP_201_CREATED)
        except Exception as e:
            return utils.CustomResponse.Failure(str(e), status=status.HTTP_400_BAD_REQUEST)
# class UpdatePriceChange(APIView):
#     permission_classes = ()
#     authentication_classes = ()

#     def post(self, request):
#         id, price, =  request.data['device_id'], request.data['new_price'] #get request body
#         current_date = datetime.now()
#         todays_time = current_date.strftime("%Y-%m-%d %H:%M:%S")
#         # get details of pump
#         pump = get_object_or_404(models.Pump, Device_id=id)
#         # get site attached to pump
#         site_id = pump.Site.Site_id
#         # count number of pumps in site
#         count_pump_in_site = models.Pump.objects.filter(Site=site_id).count()

#         try:

#             price_data = models.PumpPriceChangeRecord.objects.filter(site_id=site_id, new_price=price).last()
#             # Prevent device from receiving multiple update for same device_id
#             # if id in price_data.pump_data['device_id']:
#             #     return utils.CustomResponse.Failure("Pump ID already exist", status=status.HTTP_400_BAD_REQUEST)

#             # condition for updating price change request data to Partially Effected
#             if (price_data.pump_count < price_data.total_pump_in_site) and (price_data.total_pump_in_site - price_data.pump_count > 1):

#                 # add pump details to pump data and increase count
#                 price_data.pump_count +=1
#                 price_data.pump_data['device_id'].append(pump.Device.Device_id)
#                 price_data.pump_data['mac_address'].append(pump.Device.Device_unique_address)
#                 price_data.save()
#                 #  there's no point to keep updating the pump_price_change_status of PriceChangeRequestData Table;
#                 # This condition ensures that the status of pump_price_change_status in PriceChangeRequestData is Partially Effected
#                 return utils.CustomResponse.Success("Status Updated Successfully", status=status.HTTP_200_OK)
#             else:
#                 # When the difference btw total_pump_in_site and pump_count is not > 1, it is expected that the last pump status is been received
#                 # delete pump price record in PumpPriceChangeRecord Table, update pump_price_change_status of PriceChangeRequestData to Fully Effected
#                 price_data.delete()
#                 price_status = models.PriceChangeRequestData.objects.get(Site=site_id,New_price=price,Approved=True)
#                 price_status.pump_price_change_status = "Fully Effected"
#                 price_status.save()
#                 return utils.CustomResponse.Success("Status Updated Successfully", status=status.HTTP_200_OK)

#         except:
#             obj, created = models.PumpPriceChangeRecord.objects.get_or_create(site_id=site_id,total_pump_in_site=count_pump_in_site,new_price=price,
#                 pump_data={
#                     'device_id':[pump.Device.Device_id],
#                     'mac_address': [pump.Device.Device_unique_address]
#                 },
#                 time_added = todays_time
#             )
#             obj.pump_count +=1
#             obj.save()
#             # for sites with only one pump, update status to Fully Effected, else to Partially effected
#             if (obj.total_pump_in_site == obj.pump_count):
#                 try:
#                     price_status = models.PriceChangeRequestData.objects.filter(Site=site_id,New_price=price,Approved=True).last()
#                     price_status.pump_price_change_status = "Fully Effected"
#                     price_status.save()
#                     # delete PumpPriceChangeRecord record
#                     obj.delete()
#                 except:
#                     return utils.CustomResponse.Failure("Record Not Found", status=status.HTTP_400_BAD_REQUEST)
#             else:
#                 try:
#                     price_status = models.PriceChangeRequestData.objects.filter(Site=site_id,New_price=price,Approved=True).last()
#                     price_status.pump_price_change_status = "Partially Effected"
#                     price_status.save()
#                 except:
#                     return utils.CustomResponse.Failure("Record Not Found", status=status.HTTP_400_BAD_REQUEST)
#             return utils.CustomResponse.Success("Status Received", status=status.HTTP_201_CREATED)


class TransactionLogger(APIView, LargeResultsSetPagination):
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.TransactionDataSerializer

    def post(self, request):
        # fix for double transaction
        log = request.data
        index = 0
        for each in log:
            computed_total = each['Raw_transaction_price_per_unit'] * \
                each['Transaction_raw_volume']
            each['computed_total'] = "{:.3f}".format(computed_total)
            transaction_data = {
                "local_id": each['local_id'],
                "Nozzle_address": each["Nozzle_address"],
                "Transaction_start_time": str(each["Transaction_start_time"]),
                "Transaction_stop_time": str(each["Transaction_stop_time"]),
                "Transaction_raw_volume": each["Transaction_raw_volume"],
                "Transaction_raw_amount": each["Transaction_raw_amount"],
                "Raw_transaction_price_per_unit": each["Raw_transaction_price_per_unit"],
                "computed_total": "{:.3f}".format(computed_total),
                "Pump_mac_address": each["Pump_mac_address"],
                "Transaction_start_pump_totalizer_volume": each["Transaction_start_pump_totalizer_volume"],
                "Transaction_stop_pump_totalizer_volume": each["Transaction_stop_pump_totalizer_volume"],
                "Transaction_start_pump_totalizer_amount": each["Transaction_start_pump_totalizer_amount"],
                "Transaction_stop_pump_totalizer_amount": each["Transaction_stop_pump_totalizer_amount"],
                "Device": each["Device"],
                "Site": each["Site"],
                "Raw_nozzle_address": each["Nozzle_address"],
            }
            if not models.TransactionData.objects.filter(local_id=each.get('local_id')).exists():
                passed_data = self.serializer_class(
                    data=log[index], many=False)
                if passed_data.is_valid():
                    passed_data.save()
            index = index + 1
            # Trigger task in background
            executor.submit(
                save_smart_pump_dashboard_data_to_redis, transaction_data['Site'])
        return utils.UpdatedCustomResponse.Success('success', status=status.HTTP_201_CREATED)

    def get(self, request):
        ''' Min query params: Site and Time Range '''
        Nozzle_address = request.GET['Nozzle_addresses'].split(',')
        Site_id = request.GET['Site_id']
        Pump_mac_address = request.GET['Pump_mac_address'].split(',')
        transaction_time = request.GET['period'].replace("+", " ").split(",")
        products = request.GET['products'].split(",")
        currency_symbol = get_currency(Site_id)
        transaction_history_type = u.TransactionHistoryFactory(
            Nozzle_address, Site_id, Pump_mac_address, transaction_time, products).get_transaction_history_type()
        # get transactions
        if transaction_history_type is not None:   
            returned = transaction_history_type.get_transactions()
            results = self.paginate_queryset(returned, request, view=self)
            # serializer = TransactionDataResponseSerializer(results, many=True)
            # print(results)

            rpt = []
            # for i in serializer.data:
            for i in results:
                # user_id = i['user_id']
                user_name = ""
                try:
                    # user_ = models.User.objects.filter(id=user_id).values("Name")
                    # user_name = user_[0]["Name"]
                    pass
                except:
                    pass

                # computed_total = i['computed_total'] or 0
                _rpt = {
                    # 'Nozzle_address':
                    'Transaction_start_time': i[0],
                    'Transaction_stop_time': i[0],
                    # 'Transaction_raw_amount':i[1],
                    'Transaction_raw_amount': '{:,.2f}'.format(i[7]),
                    'computed_total': '{:,.2f}'.format(i[3]),
                    'Product_mac_address': i[4],
                    'Product_pump_name': i[4],
                    'Nozzle_name': i[1],
                    'Product_name': i[6],
                    # 'Transaction_raw_volume':i[6],
                    'Transaction_raw_volume': '{:,.3f}'.format(i[7]),
                    'site_name': i[5],
                    # 'Raw_transaction_price_per_unit':i[8]
                    'Raw_transaction_price_per_unit': '{:,.2f}'.format(i[2])
                }

                # rpt.append(OrderedDict(
                # [
                #     # ('Nozzle_address', i['Nozzle_address']),
                #     # ('Transaction_start_time', i['Transaction_start_time']),
                #     # ('Transaction_stop_time', i['Transaction_stop_time']),
                #     # ('Transaction_raw_volume', "{:,.3f}".format(i['Transaction_raw_volume'])),
                #     # ('Transaction_raw_amount', i['Transaction_raw_amount']),
                #     # ('computed_total', "{:,.3f}".format(float(computed_total))),
                #     # ('Raw_transaction_price_per_unit', i['Raw_transaction_price_per_unit']),
                #     # ('Pump_mac_address', i['Pump_mac_address']),
                #     # ('Product_name', i['Product_name']),
                #     # ('user_name', user_name)
                # ]))
                rpt.append(_rpt)
            data, headers = self.get_paginated_response(rpt)

            # todo:Cache this response into Redis for later use
            return utils.CustomResponse.Success(data=[data], headers=headers)
            # return utils.CustomResponse.Success(results)

        else:
            return utils.CustomResponse.Failure('Invalid form/request data', status=status.HTTP_400_BAD_REQUEST)


class PicRemoteConfig(APIView):
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.PicNozzleSerializer

    def post(self, request):
        # configs = get_remote_configs(request.data['mac_address'])
        update_device_online_status(request.data['mac_address'])

        nozzles = self.serializer_class(models.Nozzle.objects.filter(
            Pump__Device__Device_unique_address=request.data['mac_address'], Pump__Pushed_to_device=True, Pump__Activate=True), many=True)
        data = []
        for item in range(len(nozzles.data)):
            site_id = nozzles.data[item]['Site_id']
            Device_id = nozzles.data[item]['Device_id']
            Nozzle_count = nozzles.data[item]['Nozzle_count']
            Pump_protocol = nozzles.data[item]['Pump_protocol']
            Nozzle_addres = nozzles.data[item]['Nozzle_address']
            Nozzle_address_hex_code = nozzles.data[item]['Nozzle_address_hex_code']

            Price = nozzles.data[item]['Price']
            if not Price or Price == None:
                Price = "0.00"
            Price_time = nozzles.data[item]['Price_time']
            if not Price_time or Price_time == None:
                Price_time = datetime.now()

            Decimal_setting_price_unit = nozzles.data[item]['Decimal_setting_price_unit']
            Decimal_setting_amount = nozzles.data[item]['Decimal_setting_amount']
            Decimal_setting_volume = nozzles.data[item]['Decimal_setting_volume']
            data_pic_config = {
                "Site_id": site_id,
                "Device_id": Device_id,
                "Nozzle_count": Nozzle_count,
                "Pump_protocol": Pump_protocol,
                "Nozzle_address": Nozzle_address_hex_code,
                "Price": Price,
                "Price_time": Price_time,
                "Decimal_setting_price_unit": Decimal_setting_price_unit,
                "Decimal_setting_amount": Decimal_setting_amount,
                "Decimal_setting_volume": Decimal_setting_volume
            }
            data.append(data_pic_config)
        if len(data) > 0:
            update_status(request.data['mac_address'])
            return utils.CustomResponse.Success(data, status=status.HTTP_200_OK)
        else:
            return utils.CustomResponse.Failure(f"No Config found for this Mac Address: {request.data['mac_address']}", status=status.HTTP_404_NOT_FOUND)


class PicRemoteConfigService(APIView):
    '''
    A Class where Remote Config Service get its RemoteConfig from.
    This needs to be maintaned if new changes are made to the PicRemoteConfig Class.
    '''
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.PicNozzleSerializer

    def post(self, request):
        # configs = get_remote_configs(request.data['mac_address'])
        update_device_online_status(request.data['mac_address'])

        nozzles = self.serializer_class(models.Nozzle.objects.filter(
            Pump__Device__Device_unique_address=request.data['mac_address'], Pump__Pushed_to_device=True, Pump__Activate=True), many=True)
        data = []
        for item in range(len(nozzles.data)):
            site_id = nozzles.data[item]['Site_id']
            Device_id = nozzles.data[item]['Device_id']
            Site_name = nozzles.data[item]['Site_name']
            Device_name = nozzles.data[item]['Device_name']
            Pump_name = nozzles.data[item]['Pump_name']
            Nozzle_count = nozzles.data[item]['Nozzle_count']
            Pump_protocol = nozzles.data[item]['Pump_protocol']
            Nozzle_addres = nozzles.data[item]['Nozzle_address']
            Nozzle_address_hex_code = nozzles.data[item]['Nozzle_address_hex_code']
            price_data = models.PriceChangeRequestData.objects.filter(
                Site=site_id, Product=nozzles.data[item]['Product'], Approved=True).last()
            # This check is put in place to cater for when you create a new pump and it has no price change request
            try:
                Price = round(float(price_data.New_price), 1)
            except:
                Price = round(float(0.0), 1)

            if not Price or Price == None:
                Price = round(float(0.0), 1)

            # This check is put in place to cater for when you create a new pump and it has no price change request
            try:
                Price_time = price_data.Scheduled_time
            except:
                Price_time = datetime.now()

            # Price_time = price_data.Scheduled_time
            # Price_time = price_data.Scheduled_time
            if not Price_time or Price_time == None:
                Price_time = datetime.now()

            Decimal_setting_price_unit = nozzles.data[item]['Decimal_setting_price_unit']
            Decimal_setting_amount = nozzles.data[item]['Decimal_setting_amount']
            Decimal_setting_volume = nozzles.data[item]['Decimal_setting_volume']
            data_pic_config = {
                "Site_id": site_id,
                "Device_id": Device_id,
                "Site_name": Site_name,
                "Device_name": Device_name,
                "Pump_name": Pump_name,
                "Nozzle_count": Nozzle_count,
                "Pump_protocol": Pump_protocol,
                "Nozzle_address": Nozzle_address_hex_code,
                "Price": Price,
                "Price_time": Price_time,
                "Decimal_setting_price_unit": Decimal_setting_price_unit,
                "Decimal_setting_amount": Decimal_setting_amount,
                "Decimal_setting_volume": Decimal_setting_volume
            }
            data.append(data_pic_config)
        if len(data) > 0:
            update_status(request.data['mac_address'])
            return utils.CustomResponse.Success(data, status=status.HTTP_200_OK)
        else:
            return utils.CustomResponse.Failure(f"No Config found for this Mac Address: {request.data['mac_address']}", status=status.HTTP_404_NOT_FOUND)


class VeloxConfig(APIView):

    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.VeloxSerializer

    def post(self, request):
        try:
            pump = models.Nozzle.objects.filter(
                Pump__Device__Device_id=request.data['Device_id'], Pump__Pushed_to_device=True, Pump__Activate=True)[0]
        except:
            return utils.CustomResponse.Failure("Device id not found", status=status.HTTP_400_BAD_REQUEST)

        serialized = self.serializer_class(pump)
        return utils.CustomResponse.Success(serialized.data, status=status.HTTP_200_OK)


class RemoteConfig(APIView):
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.NozzleSerializer

    def get(self, request):
        nozzles = self.serializer_class(models.Nozzle.objects.filter(
            Pump__Device__Device_unique_address=request.data['mac_address'], Pump__Pushed_to_device=True, Pump__Activate=True), many=True)
        if nozzles.data:
            update_pump_status = update_status(request.data['mac_address'])
            update_device_online_status(request.data['mac_address'])

            return utils.CustomResponse.Success(nozzles.data, status=status.HTTP_200_OK)
        else:
            return utils.CustomResponse.Failure(f"No Config found for this Mac Address: {request.data['mac_address']}", status=status.HTTP_404_NOT_FOUND)


class PushConfigToDevice(APIView):
    authentication_classes = ()
    permission_classes = ()
    '''
        Endpoint for pushing changes in remote config to device
    '''
    serializer_class = serializer.PumpSerializer

    def post(self, request, *args, **kwargs):
        pk = self.kwargs.get('pk')
        pump = get_object_or_404(models.Pump, pk=pk)
        pump.Pushed_to_device = True
        pump.save()
        serializer = self.serializer_class(pump)
        return utils.CustomResponse.Success(serializer.data)


class PriceChange(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PriceChangePermission)]
    serializer_class = serializer.PriceChangeSerializer

    def get(self, request):
        serialized_data = self.serializer_class(models.PriceChange.objects.filter(
            mac_address=request.data['mac_address'], Approved=True, Received=False, Rejected=False), many=True)
        if len(serialized_data.data) > 0:
            models.PriceChange.objects.filter(
                mac_address=request.data['mac_address'], Approved=True, Received=False).update(Received=True)
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_200_OK)

    def post(self, request):
        try:
            # Validate required fields
            required_fields = ['Company', 'Site',
                               'Product', 'New_price', 'Scheduled_time']
            missing_fields = [
                field for field in required_fields if field not in request.data]
            if missing_fields:
                return utils.CustomResponse.Failure(
                    f"Missing required fields: {', '.join(missing_fields)}",
                    status=status.HTTP_400_BAD_REQUEST
                )

            serialized_data = serializer.PriceChangeRequestSerializer(
                data=request.data, context={'request': request})
            if not serialized_data.is_valid():
                errors = serialized_data.errors
                error_messages = []

                if 'Scheduled_time' in errors:
                    error_messages.append(
                        "Invalid datetime format. Please use ISO format (YYYY-MM-DDThh:mm:ssZ)"
                    )
                if 'New_price' in errors:
                    error_messages.append(
                        "Invalid price format. Price must be a positive number with up to 2 decimal places"
                    )
                if 'Company' in errors:
                    error_messages.append("Invalid Company ID")
                if 'Site' in errors:
                    error_messages.append("Invalid Site ID")
                if 'Product' in errors:
                    error_messages.append("Invalid Product ID")

                if not error_messages:
                    error_messages = [str(error)
                                      for error in serialized_data.errors.values()]

                return utils.CustomResponse.Failure(
                    " | ".join(error_messages),
                    status=status.HTTP_422_UNPROCESSABLE_ENTITY
                )

            # Save with initiator
            instance = serialized_data.save(Initiator=request.user)

            # Create response data
            response_data = {
                "id": instance.id,
                "New_price": float(instance.New_price),
                "Scheduled_time": instance.Scheduled_time.strftime("%Y-%m-%d %H:%M:%S"),
                "Approved": instance.Approved,
                "Rejection_note": instance.Rejection_note,
                "Approval_or_rejection_time": instance.Approval_or_rejection_time.strftime("%Y-%m-%d %H:%M:%S") if instance.Approval_or_rejection_time else None,
                "db_fill_time": instance.db_fill_time.strftime("%Y-%m-%d %H:%M:%S"),
                "pump_price_change_status": instance.pump_price_change_status,
                "Product": instance.Product.Product_id,
                "Company": instance.Company.Company_id,
                "Site": instance.Site.Site_id,
                "Initiator": instance.Initiator.id,
                "Actor": instance.Actor.id if instance.Actor else None
            }

            return utils.CustomResponse.Success(
                response_data,
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return utils.CustomResponse.Failure(
                f"Error processing request: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        try:
            pricechange_id = request.data.get('id', None)
            pc_status = request.data.get('Approval', None)
            rejection_note = request.data.get('Rejection_note', None)

            # Check if record exists and get its current status
            price_change = models.PriceChangeRequestData.objects.filter(
                pk=pricechange_id).first()
            if not price_change:
                return utils.CustomResponse.Failure(
                    "Price change request not found",
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if already approved
            if price_change.Approved is True:
                return utils.CustomResponse.Failure(
                    "This price change request has already been approved",
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update approval status and related fields
            price_change.Approved = pc_status
            price_change.Actor = request.user
            price_change.Rejection_note = rejection_note
            price_change.Approval_or_rejection_time = datetime.now()
            price_change.save()

            if pc_status:
                # Create price history if approved
                success, message = u.createProductPriceHistory(request)
                if not success:
                    return utils.CustomResponse.Failure(
                        f"Price change failed: {message}",
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Send remote config update
                send_remote_config(price_change.Site.Site_id)

            return utils.CustomResponse.Success(
                data="Price approved successfully" if pc_status else "Price rejected successfully",
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return utils.CustomResponse.Failure(
                f"Error processing price change: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RawPriceChangeData(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup | permissions.IsActiveAuthenticatedProductAdmin |
                           permissions.IsCompanyAdmin | permissions.RawPriceChangeDataPemission)]
    serializer_class = serializer.RawPriceChangeDataSerializer

    def get(self, request):
        serialized_data = serializer.GetRawPriceChangeDataSerializer(
            models.RawPriceChangeData.objects.filter(Site=request.GET['Site_id']), many=True)
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_200_OK)

    def post(self, request):
        serialized_data = self.serializer_class(data=request.data)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        serialized_data.save()
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_201_CREATED)

    def patch(self, request):
        pricechange_id = request.data.get('id', None)
        pc_status = request.data.get('Approval', None)
        models.RawPriceChangeData.objects.filter(
            pk=pricechange_id).update(Approved=pc_status)
        # split pricechange into nozzles in site
        if pc_status == True:
            u.changeNozzlePrice(request.data)
            return utils.CustomResponse.Success(data="Price change approved successfully", status=status.HTTP_201_CREATED)
        return utils.CustomResponse.Success(data="Price change rejected successfully", status=status.HTTP_201_CREATED)


class SitePriceChangeRequest(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PriceChangePermission)]
    serializer_class = serializer.PriceChangeRequestSerializer

    def get(self, request):
        serialized_data = serializer.GetPriceRequestDataSerializer(
            models.PriceChangeRequestData.objects.filter(Site=request.GET['Site_id']), many=True)
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_200_OK)

    def post(self, request):
        try:
            # Validate required fields
            required_fields = ['Company', 'Site',
                               'Product', 'New_price', 'Scheduled_time']
            missing_fields = [
                field for field in required_fields if field not in request.data]
            if missing_fields:
                return utils.CustomResponse.Failure(
                    f"Missing required fields: {', '.join(missing_fields)}",
                    status=status.HTTP_400_BAD_REQUEST
                )

            serialized_data = self.serializer_class(data=request.data)

            if not serialized_data.is_valid():
                errors = serialized_data.errors
                error_messages = []

                if 'Scheduled_time' in errors:
                    error_messages.append(
                        "Invalid datetime format. Please use ISO format (YYYY-MM-DDThh:mm:ssZ)"
                    )
                if 'New_price' in errors:
                    error_messages.append(
                        errors['New_price'][0]
                    )
                if 'Company' in errors:
                    error_messages.append("Invalid Company ID")
                if 'Site' in errors:
                    error_messages.append("Invalid Site ID")
                if 'Product' in errors:
                    error_messages.append("Invalid Product ID")
                if 'non_field_errors' in errors:
                    error_messages.append(
                        errors['non_field_errors'][0]
                    )
                if not error_messages:
                    error_messages = [str(error)
                                      for error in serialized_data.errors.values()]

                return utils.CustomResponse.Failure(
                    " | ".join(error_messages),
                    status=status.HTTP_422_UNPROCESSABLE_ENTITY
                )

            # Save with initiator
            instance = serialized_data.save(Initiator=request.user)

            # Create response data
            response_data = {
                "id": instance.id,
                "New_price": float(instance.New_price),
                "Scheduled_time": instance.Scheduled_time.strftime("%Y-%m-%d %H:%M:%S"),
                "Approved": instance.Approved,
                "Rejection_note": instance.Rejection_note,
                "Approval_or_rejection_time": instance.Approval_or_rejection_time.strftime("%Y-%m-%d %H:%M:%S") if instance.Approval_or_rejection_time else None,
                "db_fill_time": instance.db_fill_time.strftime("%Y-%m-%d %H:%M:%S"),
                "pump_price_change_status": instance.pump_price_change_status,
                "Product": instance.Product.Product_id,
                "Company": instance.Company.Company_id,
                "Site": instance.Site.Site_id,
                "Initiator": instance.Initiator.id,
                "Actor": instance.Actor.id if instance.Actor else None
            }

            return utils.CustomResponse.Success(
                response_data,
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return utils.CustomResponse.Failure(
                f"Error processing request: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        try:
            pricechange_id = request.data.get('id', None)
            pc_status = request.data.get('Approval', None)
            rejection_note = request.data.get('Rejection_note', None)

            # Check if record exists and get its current status
            price_change = models.PriceChangeRequestData.objects.filter(
                pk=pricechange_id).first()
            if not price_change:
                return utils.CustomResponse.Failure(
                    "Price change request not found",
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if already approved
            if price_change.Approved is True:
                return utils.CustomResponse.Failure(
                    "This price change request has already been approved",
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Update approval status and related fields
            price_change.Approved = pc_status
            price_change.Actor = request.user
            price_change.Rejection_note = rejection_note
            price_change.Approval_or_rejection_time = datetime.now()
            price_change.save()

            if pc_status:
                # Create price history if approved
                success, message = u.createProductPriceHistory(request)
                if not success:
                    return utils.CustomResponse.Failure(
                        f"Price change failed: {message}",
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Send remote config update
                send_remote_config(price_change.Site.Site_id)

            return utils.CustomResponse.Success(
                data="Price approved successfully" if pc_status else "Price rejected successfully",
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            return utils.CustomResponse.Failure(
                f"Error processing price change: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FilterPriceChangeRequest(APIView):
    '''
    filter PriceChangeRequestData is in the format
    '''
    authentication_classes = ()
    permission_classes = ()
    serializer_class = serializer.FilterPriceChangeSerializer

    def post(self, request):
        data = []
        try:
            site, product, starttime, stoptime, stat = request.data['Site'], request.data[
                'Product'], request.data['start_time'], request.data['stop_time'], request.data['Approved']
            if stat == str('Null'):
                result_data = models.PriceChangeRequestData.objects.filter(
                    Site__Site_id=site, Product__Product_id=product, Approved__isnull=True, Scheduled_time__range=[starttime, stoptime]).order_by('-id')
            else:
                result_data = models.PriceChangeRequestData.objects.filter(
                    Site__Site_id=site, Product__Product_id=product, Approved=stat, Scheduled_time__range=[starttime, stoptime]).order_by('-id')
            for each in result_data:
                if each.Actor is None:
                    actor = None
                else:
                    actor = each.Actor.Name
                try:
                    price = {
                        "id": each.id,
                        "Product": {
                            "Product_id": each.Product.Product_id,
                            "Name": each.Product.Name,
                            "Code": each.Product.Code
                        },
                        "Initiator": each.Initiator.Name,
                        "Actor": actor,
                        "Company_name": each.Company.Name,
                        "Site_name": each.Site.Name,
                        "New_price": each.New_price,
                        "Scheduled_time": each.Scheduled_time,
                        "Approved": each.Approved,
                        "Rejection_note": each.Rejection_note,
                        "Approval_or_rejection_time": each.Approval_or_rejection_time,
                        "db_fill_time": each.db_fill_time,
                        "pump_price_change_status": each.pump_price_change_status,
                        "Company": each.Company.Company_id,
                        "Site": each.Site.Site_id
                    }
                    data.append(price)
                except Exception as e:
                    pass
            return utils.CustomResponse.Success(data, status=status.HTTP_200_OK)
        except:
            return utils.CustomResponse.Failure("Bad Request", status=status.HTTP_400_BAD_REQUEST)


class SearchPriceChangeRequestData(APIView):

    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        kword, role_id = request.data['q'], request.data['Role_Id']
        data = []
        if not kword:
            return utils.CustomResponse.Failure("No Key Word", status=status.HTTP_204_NO_CONTENT)
        try:
            if role_id == 0:
                result_data = models.PriceChangeRequestData.objects.filter(
                    Q(Site__Name__icontains=kword) | Q(Product__Name__icontains=kword)).order_by('-db_fill_time')
            else:
                result_data = models.PriceChangeRequestData.objects.filter(
                    Q(Site__Name__icontains=kword) | Q(Product__Name__icontains=kword), Company__Company_id=role_id)
            for each in result_data:
                try:
                    price = {
                        "id": each.id,
                        "Product": {
                            "Product_id": each.Product.Product_id,
                            "Name": each.Product.Name,
                            "Code": each.Product.Code
                        },
                        "Initiator": each.Initiator.Name,
                        "Actor": each.Actor.Name,
                        "Company_name": each.Company.Name,
                        "Site_name": each.Site.Name,
                        "New_price": each.New_price,
                        "Scheduled_time": each.Scheduled_time,
                        "Approved": each.Approved,
                        "Rejection_note": each.Rejection_note,
                        "Approval_or_rejection_time": each.Approval_or_rejection_time,
                        "db_fill_time": each.db_fill_time,
                        "pump_price_change_status": each.pump_price_change_status,
                        "Company": each.Company.Company_id,
                        "Site": each.Site.Site_id
                    }
                    data.append(price)
                except:
                    pass
            return utils.CustomResponse.Success(data, status=status.HTTP_200_OK)
        except Exception as e:
            return utils.CustomResponse.Failure("Bad Request", status=status.HTTP_400_BAD_REQUEST)


class CompanyPriceChangeRequest(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PriceChangePermission)]
    serializer_class = serializer.CompanyProductPriceChangeRequestSerializer

    def get(self, request):
        serialized_data = serializer.GetCompanyPriceRequestDataSerializer(
            models.CompanyProductPriceRequest.objects.filter(Company=request.GET['Company_id']), many=True)
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_200_OK)

    def post(self, request):
        serialized_data = self.serializer_class(data=request.data)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
        serialized_data.save(Initiator=request.user)
        # Notify Company Admin of new price change request for company
        # notification params:price_change_request object, designation, is_initial_price
        # decouple notification system from celery
        send_price_change_notification(serialized_data.data, 'Company', True)
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_201_CREATED)

    def patch(self, request):
        # endpoint to authorize/reject price change request for  all sites in a company
        pricechange_id = request.data.get('id', None)
        pc_status = request.data.get('Approval', None)
        rejection_note = request.data.get('Rejection_note', None)
        models.CompanyProductPriceRequest.objects.filter(
            pk=pricechange_id).update(Approved=pc_status, Actor=request.user, Rejection_note=rejection_note, Approval_or_rejection_time=datetime.now())
        # create Product_Price_History Object
        if pc_status == True:
            u.updateCompanyPriceOnProductPriceHistory(request)
            # Notify Initiator of Price Approval
            deserialized_data = self.serializer_class(
                models.CompanyProductPriceRequest.objects.get(
                    pk=pricechange_id)
            )
            # decouple notification system from celery
            send_price_change_notification(
                deserialized_data.data, 'Company', False)
            return utils.CustomResponse.Success(data="Price approved successfully", status=status.HTTP_201_CREATED)
        # notify initiator of Price Rejected
        deserialized_data = self.serializer_class(
            models.CompanyProductPriceRequest.objects.get(pk=pricechange_id)
        )
        # decouple notification system from celery
        send_price_change_notification(
            deserialized_data.data, 'Company', False)
        return utils.CustomResponse.Success(data="Price rejected successfully", status=status.HTTP_201_CREATED)


class PriceExecutionLogger(APIView):
    '''API to track execution of new price on the device '''
    serializer_class = serializer.DevicePriceExecutionSerializer
    permission_classes = ()
    authentication_classes = ()

    def post(self, request):
        serialized_data = self.serializer_class(data=request.data, many=True)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_422_UNPROCESSABLE_ENTITY)

        serialized_data.save()
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_201_CREATED)


class NewPumpView(APIView):
    authentication_classes = ()
    permission_classes = ()
    CACHE_KEY_PREFIX = 'all_pumps'

    def get(self, request):
        cached_pumps_data = django_cache.get(f'{self.CACHE_KEY_PREFIX}')
        if cached_pumps_data is None:
            print("Fetching all pumps data from database")
            all_pumps = models.Pump.objects.prefetch_related(
                "Device", "Site", "Pumpbrand", "nozzles").all()
            serializers = serializer.NewPumpSerializer(all_pumps, many=True)
            django_cache.set(f'{self.CACHE_KEY_PREFIX}', serializers.data)
            return utils.CustomResponse.Success(data=serializers.data, status=status.HTTP_200_OK)
        else:
            print("Using cached pumps data")
            return utils.CustomResponse.Success(data=cached_pumps_data, status=status.HTTP_200_OK)


class Pumps(APIView):
    # permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup | permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PumpPermission)]
    CACHE_KEY_PREFIX = 'all_pumps'
    authentication_classes = ()
    permission_classes = ()
    serializer_class = serializer.PumpSerializer

    def get(self, request):
        """
            OLD IMPLEMENTATION STARTS HERE
        """
        # data = []
        # device_data = get_pumps()
        # for item in device_data:
        #     if item[56] == 1:
        #         config_status = True

        #     else:
        #         config_status = False
        #     try:
        #         id = item[0]
        #         nozzle = get_nozzle(id)
        #         for noz in nozzle:
        #             product_id = noz[9]
        #             break

        #         product = get_product(product_id)

        #         try:
        #             device = {
        #                 "id": id,
        #                 "Device": {
        #                     "Device_id": item[7],
        #                     "Name": item[8],
        #                     "Device_unique_address": item[12],
        #                     "Phone_number": item[10],
        #                     "Created_at": item[11],
        #                     "Updated_at": item[9],
        #                     "Deleted_at": item[13],
        #                     "transmit_interval": item[16],
        #                     "Available": item[15],
        #                     "Active": item[14],
        #                     "ForPump": item[18],
        #                     "Used": item[19],
        #                     "Company": item[17]
        #                 },
        #                 "Site": {
        #                     "Site_id": item[21],
        #                     "Name": item[22],
        #                     "Country": item[41],
        #                     "State": item[23],
        #                     "City": item[24],
        #                     "Address": item[25],
        #                     "Latitude": item[45],
        #                     "Longitude": item[47],
        #                     "Location_status": item[46],
        #                     "Site_type": item[26],
        #                     "Notes": item[27],
        #                     "SIM_provided_details": item[28],
        #                     "Number_of_tanks": item[43],
        #                     "Reorder_mail": item[44],
        #                     "Critical_level_mail": item[42],
        #                     "Contact_person_name": item[29],
        #                     "Contact_person_designation": item[30],
        #                     "Contact_person_mail": item[31],
        #                     "Contact_person_phone": item[32],
        #                     "Created_at": item[33],
        #                     "Updated_at": item[34],
        #                     "Deleted_at": item[35],
        #                     "Active": item[36],
        #                     "Communication_status": item[37],
        #                     "Communication_update_time": item[38],
        #                     "Email_Notification": item[48],
        #                     "genhours_access": item[49],
        #                     "smarttank_access": item[51],
        #                     "smartpump_access": item[50],
        #                     "Company": item[39],
        #                     "Device": item[40]
        #                 },
        #                 "Pumpbrand": {
        #                     "id": item[54],
        #                     "Name": item[55],
        #                     "OEM": item[56]
        #                 },
        #                 "nozzles": [
        #                     {
        #                         "id": nozzle[0][0],
        #                         "Pump_mac_name": item[12],
        #                         "Price": "null",
        #                         "Product_name": product[0][0],
        #                         "Price_time": "null",
        #                         "First_initial_price": None,
        #                         "Name": nozzle[0][1],
        #                         "Nozzle_address": nozzle[0][2],
        #                         "Nozzle_address_hex_code": nozzle[0][11],
        #                         "Decimal_setting_price_unit": nozzle[0][3],
        #                         "Decimal_setting_amount": nozzle[0][4],
        #                         "Decimal_setting_volume": nozzle[0][5],
        #                         "Totalizer_at_installation": nozzle[0][6],
        #                         "Display_unit": nozzle[0][7],
        #                         "Nominal_flow_rate": nozzle[0][8],
        #                         "Product": product[0][0],
        #                         "Pump": nozzle[0][10]
        #                     },
        #                     {
        #                         "id": nozzle[1][0],
        #                         "Pump_mac_name": item[12],
        #                         "Price": "null",
        #                         "Product_name": product[0][0],
        #                         "Price_time": "null",
        #                         "First_initial_price": None,
        #                         "Name": nozzle[1][1],
        #                         "Nozzle_address": nozzle[1][2],
        #                         "Nozzle_address_hex_code": nozzle[1][11],
        #                         "Decimal_setting_price_unit": nozzle[1][3],
        #                         "Decimal_setting_amount": nozzle[1][4],
        #                         "Decimal_setting_volume": nozzle[1][5],
        #                         "Totalizer_at_installation": nozzle[1][6],
        #                         "Display_unit": nozzle[1][7],
        #                         "Nominal_flow_rate": nozzle[1][8],
        #                         "Product": product[0][0],
        #                         "Pump": nozzle[1][10]
        #                     }
        #                 ],
        #                 "Name": item[1],
        #                 "Pump_protocol": item[2],
        #                 "Nozzle_count": item[3],
        #                 "Note": item[4],
        #                 "Activate": item[5],
        #                 "Pushed_to_device": item[6],
        #                 "use_deciaml_config": config_status
        #             }
        #             data.append(device)
        #         except:
        #             pass
        #     except:
        #         pass
        # deserialized_data = self.serializer_class(
        #     models.Pump.objects.all(), many=True)

        """
            OLD IMPLEMENTATION ENDS HERE
        """

        """ NEW IMPLEMENTATION WITH CACHING STARTEGY TO IMPROVER PERFORMANCE """
        cached_pumps_data = django_cache.get(f'{self.CACHE_KEY_PREFIX}')
        if cached_pumps_data is None:
            print("Fetching all pumps data from database")
            all_pumps = models.Pump.objects.prefetch_related(
                "Device", "Site", "Pumpbrand", "nozzles").all()
            serializers = serializer.NewPumpSerializer(all_pumps, many=True)
            django_cache.set(f'{self.CACHE_KEY_PREFIX}', serializers.data)
            return utils.CustomResponse.Success(data=serializers.data, status=status.HTTP_200_OK)
        else:
            print("Using cached pumps data")
            return utils.CustomResponse.Success(data=cached_pumps_data, status=status.HTTP_200_OK)

    def post(self, request):
        device = request.data.get('Device', None)
        site = request.data.get('Site', None)
        company = request.data.get('Company', None)

        if not device:
            return utils.CustomResponse.Failure("Device is required", status=status.HTTP_400_BAD_REQUEST)

        pump_brand = request.data.get('Pumpbrand', None)

        if not pump_brand:
            return utils.CustomResponse.Failure("Pump brand is required", status=status.HTTP_400_BAD_REQUEST)

        try:
            pump_brand = models.PumpBrand.objects.get(id=pump_brand)
        except models.PumpBrand.DoesNotExist:
            return utils.CustomResponse.Failure("Pump brand not found", status=status.HTTP_400_BAD_REQUEST)
        try:
            device_record = models.Devices.objects.get(
                Device_id=request.data.get('Device', None))
        except models.Devices.DoesNotExist:
            return utils.CustomResponse.Failure("Device not found", status=status.HTTP_400_BAD_REQUEST)

        if not device_record.Active:
            return utils.CustomResponse.Failure("Device is not active", status=status.HTTP_400_BAD_REQUEST)

        device_already_pump = models.Pump.objects.filter(
            Device__Device_id=request.data.get('Device', None)).first()

        nozzle_count = request.data.get('Nozzle_count', None)
        if not nozzle_count:
            return utils.CustomResponse.Failure("Nozzle count is required", status=status.HTTP_400_BAD_REQUEST)

        if int(nozzle_count) != len(request.data['Nozzles']):
            return utils.CustomResponse.Failure("Nozzle count does not match", status=status.HTTP_400_BAD_REQUEST)
        # ensure unique nozzle address
        nozzles = request.data.get('Nozzles', None)
        if not nozzles:
            return utils.CustomResponse.Failure("A pump must have atleast one Nozzle", status=status.HTTP_400_BAD_REQUEST)
        if int(nozzle_count) != len(nozzles):
            return utils.CustomResponse.Failure("Nozzle count does not match", status=status.HTTP_400_BAD_REQUEST)

        nozzle_addresses = [item["Nozzle_address"] for item in nozzles]
        # Check for duplicate Nozzle_address
        has_duplicate = len(nozzle_addresses) != len(set(nozzle_addresses))

        if has_duplicate:
            return utils.CustomResponse.Failure("Duplicate Nozzle address found", status=status.HTTP_400_BAD_REQUEST)

        if device_already_pump:
            return utils.CustomResponse.Failure("Pump device already exist", status=status.HTTP_400_BAD_REQUEST)

        # check if all sites, devices belong to the same company

        pump_serialized_data = serializer.PumpSaveSerializer(data=request.data)
        if not pump_serialized_data.is_valid():
            return utils.CustomResponse.Failure(pump_serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        pump_exist = models.Pump.objects.filter(Name=request.data.get('Name', None), Device=request.data.get(
            'Device', None), Site=request.data.get('Site', None)).first()
        if not pump_exist:
            pump_serialized_data.save()
            pump_exist = pump_serialized_data.data

        nozzles = request.data['Nozzles']
        models.Devices.objects.filter(
            pk=request.data.get("Device", None)).update(Used=True)
        try:
            pump_reference = pump_exist.id
        except AttributeError:
            pump_reference = pump_exist['id']
        for each in nozzles:
            try:
                each['Pump'] = pump_exist.id
            except AttributeError:
                each['Pump'] = pump_exist['id']
        nozzle_serialized_data = serializer.NozzleSaveSerializer(
            data=nozzles, many=True)
        if not nozzle_serialized_data.is_valid():
            return utils.CustomResponse.Failure(nozzle_serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        nozzle_serialized_data.save()
        post_response_serializer = self.serializer_class(
            models.Pump.objects.get(pk=pump_reference)
        )
        site_value = request.data.get('Site', None)

        if isinstance(site_value, set):
            # Convert to number if it's a single-element set
            site_id = next(iter(site_value))
        else:
            site_id = site_value
        # send_remote_config(request.data.get('Site', None), False) to be executed in the background
        executor.submit(send_remote_config, site_id)
        django_cache.delete(f'{self.CACHE_KEY_PREFIX}')

        # background_thread = threading.Thread(
        #     target=send_remote_config, args=({request.data.get('Site', None)}, False))
        # # Start the thread
        # background_thread.start()
        return utils.CustomResponse.Success(post_response_serializer.data, status=status.HTTP_201_CREATED)

    def patch(self, request):
        pump_serialized_data = serializer.PumpSaveSerializer(models.Pump.objects.get(
            pk=request.data.get('id', None)), data=request.data, partial=True)
        if not pump_serialized_data.is_valid():
            return utils.CustomResponse.Failure(pump_serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        pump_serialized_data.save()

        nozzles = request.data['Nozzles']
        for each in nozzles:
            each['Pump'] = pump_serialized_data.data['id']
            try:
                each['Display_unit'] = each['Display_unit']['alias']
            except Exception as e:
                each['Display_unit'] = each['Display_unit']

            try:
                each['Product'] = each['Product']['Product_id']
            except Exception as e:
                each['Product'] = each['Product']

            try:
                self._extracted_from_patch_12(each)
            except Exception as e:
                pass

            try:
                nozzle_serialized_data = serializer.NozzleSaveSerializer(
                    models.Nozzle.objects.get(pk=each['id']), data=each, partial=True)
            except KeyError as e:
                product = models.Products.objects.get(pk=each['Product'])
                pump = models.Pump.objects.get(pk=each['Pump'])
                models.Nozzle(Name=each['Name'], Nozzle_address=each['Nozzle_address'], Decimal_setting_volume=each['Decimal_setting_volume'], Product=product, Pump=pump, Decimal_setting_price_unit=each['Decimal_setting_price_unit'],
                              Decimal_setting_amount=each['Decimal_setting_amount'], Totalizer_at_installation=each['Totalizer_at_installation'], Display_unit=each['Display_unit'], Nominal_flow_rate=each['Nominal_flow_rate']).save()

            if nozzle_serialized_data.is_valid():
                nozzle_serialized_data.save()
            else:
                return utils.CustomResponse.Failure(nozzle_serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)
        response_serializer = self.serializer_class(
            models.Pump.objects.get(pk=request.data.get('id', None)))

        site_value = request.data.get('Site', None)

        if isinstance(site_value, set):
            # Convert to number if it's a single-element set
            site_id = next(iter(site_value))
        else:
            site_id = site_value
        # send_remote_config(request.data.get('Site', None), False) to be executed in the background
        executor.submit(send_remote_config, site_id)
        django_cache.delete(f'{self.CACHE_KEY_PREFIX}')
        return utils.CustomResponse.Success(response_serializer.data, status=status.HTTP_201_CREATED)

    def _extracted_from_patch_12(self, each):
        each.pop('identifier')
        each['Nozzle_address'] = int(each['Nozzle_address'])
        each['Decimal_setting_price_unit'] = int(
            each['Decimal_setting_price_unit'])
        each['Decimal_setting_amount'] = int(each['Decimal_setting_amount'])
        each['Decimal_setting_volume'] = int(each['Decimal_setting_volume'])
        each['Totalizer_at_installation'] = int(
            each['Totalizer_at_installation'])
        each['Nominal_flow_rate'] = int(each['Nominal_flow_rate'])
        each['First_initial_price'] = int(each['First_initial_price'])


class PumpDetail(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, pump_id):
        try:
            pump_record = models.Pump.objects.get(id=pump_id)
        except models.Pump.DoesNotExist:
            return utils.CustomResponse.Failure("Pump Record not found", status=status.HTTP_404_NOT_FOUND)
        serializer_ = serializer.PumpSerializer(pump_record)

        return utils.CustomResponse.Success(serializer_.data, status=200)


class PumpsActivationDetails(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PumpPermission)]
    serializer_class = serializer.PumpSerializer
    CACHE_KEY_PREFIX = 'all_pumps'

    def post(self, request, *args, **kwargs):
        pk = self.kwargs.get('pk')
        pump = get_object_or_404(models.Pump, pk=pk)
        if pump.Activate:
            pump.Activate = False
            pump.save()
        else:
            pump.Activate = True
            pump.save()
        serializer = self.serializer_class(pump)
        django_cache.delete(f'{self.CACHE_KEY_PREFIX}')
        return utils.CustomResponse.Success(serializer.data)


class Nozzle(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.NozzlePermission)]
    serializer_class = serializer.NozzleSaveSerializer
    CACHE_KEY_PREFIX = 'all_pumps'

    def get(self, request, *args, **kwargs):
        deserialized_data = self.serializer_class(
            models.Nozzle.objects.filter(Pump=request.data['Pump_id']), many=True)
        return utils.CustomResponse.Success(deserialized_data.data, status=status.HTTP_200_OK)

    def post(self, request):
        django_cache.delete(f'{self.CACHE_KEY_PREFIX}')
        serialized_data = self.serializer_class(data=request.data)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        serialized_data.save()
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_201_CREATED)

    def patch(self, request, nozzle_id):
        serialized_data = self.serializer_class(models.Nozzle.objects.get(
            pk=nozzle_id), data=request.data, partial=True)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        serialized_data.save()
        django_cache.delete(f'{self.CACHE_KEY_PREFIX}')
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_202_ACCEPTED)


class PumpBrand(APIView):
    serializer_class = serializer.PumpBrandSerializer
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PumpBrandPermission)]

    def get(self, request):
        deserialized_data = self.serializer_class(
            models.PumpBrand.objects.all(), many=True)
        return utils.CustomResponse.Success(deserialized_data.data, status=status.HTTP_200_OK)

    def post(self, request):
        serialized_data = self.serializer_class(data=request.data)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        serialized_data.save()
        return utils.CustomResponse.Success(serialized_data.validated_data, status=status.HTTP_201_CREATED)

    def patch(self, request):
        serialized_data = self.serializer_class(models.PumpBrand.objects.get(
            pk=request.data['id']), data=request.data, partial=True)
        if not serialized_data.is_valid():
            return utils.CustomResponse.Failure(serialized_data.errors, status=status.HTTP_400_BAD_REQUEST)

        serialized_data.save()
        return utils.CustomResponse.Success(serialized_data.data, status=status.HTTP_200_OK)


# class PumpsInSite(APIView):
#     serializer_class = serializer.PumpSerializer

#     def get(self, request):
#         deserialized_data = self.serializer_class(models.Pump.objects.filter(
#             Site__Site_id=request.GET['Site_id']), many=True)
#         return utils.CustomResponse.Success(deserialized_data.data, status=status.HTTP_200_OK)

class PumpsInSite(generics.ListAPIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.PumpPermission)]
    serializer_class = serializer.PumpSerializer

    def get_queryset(self):
        site_id = self.request.GET.get('Site_id')
        return models.Pump.objects.filter(Site__Site_id=site_id)

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.serializer_class(queryset, many=True)
        return utils.CustomResponse.Success(serializer.data, status=status.HTTP_200_OK)


class NozzleDashboardLatestPumpNozzlesDetail(APIView):
    def get(self, request):
        site = request.query_params.get('Site_id')

        check_site_subscribe_to_smart_pump = models.Sites.objects.filter(
            Site_id=site, smartpump_access=1
        )
        if not check_site_subscribe_to_smart_pump.exists():
            return utils.UpdatedCustomResponse.Failure(
                "This site does not subscribe to smart pump",
                status=status.HTTP_404_NOT_FOUND
            )

        get_dashboard_data = redis_client.get_pump_nozzles_latest(site)
        if get_dashboard_data:
            return utils.UpdatedCustomResponse.Success(get_dashboard_data, status=status.HTTP_200_OK)

        try:
            # Assuming getAllNozzleDetailsInSite is async, you need to run it in a sync context
            # import asyncio
            nozzle_data = u.getAllNozzleDetailsInSite(site)

            currency_symbol = get_currency(site)
            return utils.UpdatedCustomResponse.Success(
                {"data": nozzle_data, "currency_symbol": currency_symbol},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            print("Error fetching:", e)
            return utils.UpdatedCustomResponse.Failure(
                "Error fetching latest pump nozzle details",
                status=status.HTTP_404_NOT_FOUND
            )


class NozzleDashboardDailySalesAgregationByProduct(APIView):
    permission_classes = [
        (permissions.IsActiveAuthenticatedSuperAdminGroup |
         permissions.IsActiveAuthenticatedProductAdmin |
         permissions.IsCompanyAdmin |
         permissions.NozzlePermission)
    ]

    def get(self, request):
        today_date = date.today()
        site = request.query_params.get('Site_id')

        # Check subscription
        if not models.Sites.objects.filter(Site_id=site, smartpump_access=1).exists():
            return utils.UpdatedCustomResponse.Failure(
                "This site does not subscribe to smart pump",
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            # Call sync functions directly
            pms_total_volume = get_pms_total_volume(today_date, site)
            cng_total_volume = get_cng_total_volume(today_date, site)
            ago_total_volume = get_ago_total_volume(today_date, site)
            dpk_total_volume = get_dpk_total_volume(today_date, site)
            products = get_products_by_site_id(site)

            ago_sales = any(x['Name'] == 'AGO' for x in products)
            pms_sales = any(x['Name'] == 'PMS' for x in products)
            cng_sales = any(x['Name'] == 'CNG' for x in products)
            dpk_sales = any(x['Name'] == 'DPK' for x in products)

            currency_symbol = get_currency(site)
            data = {
                "pms_total_sales": "{:,.3f}".format(pms_total_volume[0][1]),
                "pms_total_volume": "{:,.3f}".format(pms_total_volume[0][0]),
                "ago_total_sales": "{:,.3f}".format(ago_total_volume[0][1]),
                "ago_total_volume": "{:,.3f}".format(ago_total_volume[0][0]),
                "dpk_total_sales": "{:,.3f}".format(dpk_total_volume[0][1]),
                "dpk_total_volume": "{:,.3f}".format(dpk_total_volume[0][0]),
                "cng_total_sales": "{:,.3f}".format(cng_total_volume[0][1]),
                "cng_total_volume": "{:,.3f}".format(cng_total_volume[0][0]),
                "ago_sales": ago_sales,
                "pms_sales": pms_sales,
                "cng_sales": cng_sales,
                "dpk_sales": dpk_sales,
                "currency_symbol": currency_symbol
            }

            return utils.UpdatedCustomResponse.Success(data, status=status.HTTP_200_OK)

        except Exception as e:
            return utils.UpdatedCustomResponse.Failure(
                f"Error retrieving daily sales aggregation by product: {e}",
                status=status.HTTP_404_NOT_FOUND
            )


class NozzleDetails(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.NozzlePermission)]

    def get(self, request):
        response = u.getNozzleDetails(request.data)
        return utils.UpdatedCustomResponse.Success(response, status=status.HTTP_200_OK)


class NozzleTrends(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.NozzlePermission)]
    serializer_class = serializer.TransactionDataSerializer

    def get(self, request):
        Nozzle_address = request.GET['Nozzle_addresses'].split(',')
        Site_id = request.GET['Site_id']
        pump_id = request.GET['pump_id']
        Pump_mac_address = request.GET['Pump_mac_address']
        transaction_time = request.GET['period'].split(",")
        transaction_queryset = models.TransactionData.objects.filter(
            Site=Site_id, Pump_mac_address=Pump_mac_address, Nozzle_address__in=Nozzle_address, Transaction_stop_time__range=transaction_time)

        dates = [x[0] for x in transaction_queryset.values_list(
            'Transaction_stop_time')]
        volumes = []
        amounts = []

        for each in Nozzle_address:
            transaction_queryset = models.TransactionData.objects.filter(
                Site=Site_id, Pump_mac_address=Pump_mac_address, Nozzle_address=each, Transaction_stop_time__range=transaction_time)
            volume = [x[0] for x in transaction_queryset.values_list(
                'Transaction_raw_volume')]
            amount = [x[0] for x in transaction_queryset.values_list(
                'Transaction_raw_amount')]
            volumes.append(volume)
            amounts.append(amount)

        nozzle_names = [
            models.Nozzle.objects.filter(
                Pump__Device__Device_unique_address=Pump_mac_address,
                Pump__id=pump_id,
                Nozzle_address=nozzle,
            ).first().Name
            for nozzle in Nozzle_address
        ]

        trendList = [dates, nozzle_names, volumes, amounts]
        totalVolume = sum(sum(each) for each in volumes)
        totalRevenue = sum(sum(each) for each in amounts)
        data = {'trendList': trendList, 'totalVolume': totalVolume,
                'totalRevenue': totalRevenue}

        return utils.CustomResponse.Success(data)


class UpdatePumpStatus(APIView):
    authentication_classes = ()
    permission_classes = ()
    CACHE_KEY_PREFIX = 'all_pumps'

    def put(self, request):
        mac_address = request.data['mac_address']
        # device = get_device_id_by_mac_address(mac_address)
        # site_id = get_pump_site_by_device_id(device)
        # current_datetime = datetime.datetime.now()
        # insert_pump_mode_switch_log(
        #     'Automation', device, current_datetime, site_id)
        update_pump_status = update_status(mac_address)
        if update_pump_status:
            django_cache.delete(f'{self.CACHE_KEY_PREFIX}')
            return utils.CustomResponse.Success(update_pump_status, status=status.HTTP_200_OK)
        else:
            return utils.CustomResponse.Failure(False, status=status.HTTP_404_NOT_FOUND)


class StationTransactionModeSwitch(APIView):
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.StationTransactionModeSwitchSerializer

    def get_current_mode(self, site):
        try:
            mode_instance = models.SmartPumpTransactionModeToggle.objects.get(
                site=site)
            return {'mode': mode_instance.mode}
        except models.SmartPumpTransactionModeToggle.DoesNotExist:
            return None

    def get(self, request):
        site = request.query_params.get('site', None)
        if site is None:
            return Response({'error': 'Site value is missing in the request'}, status=status.HTTP_400_BAD_REQUEST)
        current_mode = self.get_current_mode(site)
        return Response(current_mode, status=status.HTTP_200_OK)

    def post(self, request):
        site_id = request.data.get('site', None)
        if site_id is None:
            return Response({'error': 'Site value is missing in the request'}, status=status.HTTP_400_BAD_REQUEST)

        # Retrieve the Sites instance based on the provided site_id
        try:
            site_instance = models.Sites.objects.get(Site_id=site_id)
        except models.Sites.DoesNotExist:
            return Response({'error': 'Site with the provided ID does not exist'}, status=status.HTTP_400_BAD_REQUEST)

        serializer_instance = self.serializer_class(data=request.data)
        if serializer_instance.is_valid():
            new_mode = serializer_instance.validated_data['mode']
            if new_mode in ['POSM', 'AUTO']:
                mode_instance, _ = models.SmartPumpTransactionModeToggle.objects.get_or_create(
                    site=site_instance)
                mode_instance.mode = new_mode
                mode_instance.save()
                return Response({'mode': new_mode}, status=status.HTTP_200_OK)
            else:
                return Response({'error': 'Invalid mode'}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer_instance.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        site = request.data.get('site', None)
        if site is None:
            return Response({'error': 'Site value is missing in the request'}, status=status.HTTP_400_BAD_REQUEST)

        serializer_instance = self.serializer_class(data=request.data)
        if serializer_instance.is_valid():
            new_mode = serializer_instance.validated_data['mode']
            if new_mode in ['POSM', 'AUTO']:
                mode_instance, _ = models.SmartPumpTransactionModeToggle.objects.get_or_create(
                    site=site)
                mode_instance.mode = new_mode
                mode_instance.save()
                return Response({'mode': new_mode}, status=status.HTTP_200_OK)
            else:
                return Response({'error': 'Invalid mode'}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer_instance.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        site_id = request.query_params.get('site', None)
        if site_id is None:
            return Response({'error': 'Site value is missing in the request'}, status=status.HTTP_400_BAD_REQUEST)

        # Convert the site ID to an integer (if it's not already)
        try:
            site_id = int(site_id)
        except ValueError:
            return Response({'error': 'Invalid site value'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            mode_instance = models.SmartPumpTransactionModeToggle.objects.get(
                site_id=site_id)
            mode_instance.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except models.SmartPumpTransactionModeToggle.DoesNotExist:
            return Response({'error': 'Mode not found for the given site'}, status=status.HTTP_404_NOT_FOUND)


class StationTransactionModeSwitchList(APIView):
    permission_classes = ()
    authentication_classes = ()

    def get(self, request):
        all_modes = self.get_all_modes()
        return Response(all_modes, status=status.HTTP_200_OK)

    def get_all_modes(self):
        modes = models.SmartPumpTransactionModeToggle.objects.select_related(
            'site').all()
        return [{'site': mode.site.Name if mode.site else None, 'mode': mode.mode} for mode in modes]


class PumpModeSwitchLogs(APIView):
    permission_classes = ()
    authentication_classes = ()
    CACHE_KEY_PREFIX = 'all_pumps'

    def post(self, request):
        pump_status = request.data.get('pump_status', None)
        device_id = request.data.get('device_id', None)
        date_time = request.data.get('date_time', None)

        try:
            # Parse the date_time string into a datetime object
            # Format: DD/MM/YY,HH:MM:SS
            date_str, time_str = date_time.split(',')
            year, month, day = map(int, date_str.split('/'))
            hour, minute, second = map(int, time_str.split(':'))
            # if pump_status:
            #     pump_status = 'Automation'
            # else:
            #     pump_status = 'Manual'
            # Convert 2-digit year to 4-digit year
            year = 2000 + year

            parsed_datetime = datetime(year, month, day, hour, minute, second)

            # Create the log entry with the parsed datetime
            try:
                site = models.Pump.objects.get(Device=device_id)
            except:
                return Response({
                    'data': None,
                    'errors': 'The Device is not connected to any Pump',
                    'code': 400,
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)

            site_id = site.Site_id
            try:
                insert_pump_mode_switch_log(
                    pump_status, device_id, parsed_datetime, site_id)
                django_cache.delete(f'{self.CACHE_KEY_PREFIX}')
            except Exception as e:
                print(e, "error")
                pass

            return Response({
                'data': 'Status Updated Successfully',
                'errors': '',
                'code': 200,
                'status': 'success'
            }, status=status.HTTP_200_OK)

        except (ValueError, AttributeError, TypeError) as e:
            return Response({
                'data': None,
                'errors': 'Invalid date_time format. Expected format: DD/MM/YY,HH:MM:SS',
                'code': 400,
                'status': 'error'
            }, status=status.HTTP_400_BAD_REQUEST)

    # def get(self, request):
    #     # Retrieve the site ID from the query parameters
    #     site = request.query_params.get('site', None)
    #     if site is None or not site.isdigit():
    #         return Response({
    #             'data': None,
    #             'errors': 'Site ID is missing in the request',
    #             'code': 400,
    #             'status': 'error'
    #         }, status=status.HTTP_400_BAD_REQUEST)

    #     try:
    #         formatted_data = []
    #         pump_status = get_all_pump_mode_status(site)
    #         for log in pump_status:
    #             mac_address = get_device_mac_address(log[3])
    #             site = get_site_name_by_siteid(log[1])
    #             formatted_data.append({
    #                 'pump_status': "Automation" if log[2] == 1 else "Manual",
    #                 'site': site[0]['Name'] if site else None,
    #                 'mac_address': mac_address,
    #                 'date_time': log[4]
    #             })
    #         return Response({
    #             'data': formatted_data,
    #             'errors': '',
    #             'code': 200,
    #             'status': 'success'
    #         }, status=status.HTTP_200_OK)
    #     except Exception as e:
    #         return Response({
    #             'data': None,
    #             'errors': 'Error retrieving pump status',
    #             'code': 500,
    #             'status': 'error'
    #         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetPumpModeSwitchLogs(APIView):

    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.SitePermission)]

    def get(self, request, *args, **kwargs):
        # Retrieve the site ID from the query parameters
        site = request.query_params.get('site', None)
        start_date = request.query_params.get('start', None)
        end_date = request.query_params.get('end', None)
        device = request.query_params.get('device', None)
        if site is None or not site.isdigit():
            return Response({
                'data': None,
                'errors': 'Site ID is missing in the request',
                'code': 400,
                'status': 'error'
            }, status=status.HTTP_400_BAD_REQUEST)

        if start_date is None or end_date is None:
            return Response({
                'data': None,
                'errors': 'Either Start or End Date is missing in the request',
                'code': 400,
                'status': 'error'
            }, status=status.HTTP_400_BAD_REQUEST)

        if device is None or not device.isdigit():
            return Response({
                'data': None,
                'errors': 'Device ID is missing in the request',
                'code': 400,
                'status': 'error'
            }, status=status.HTTP_400_BAD_REQUEST)
        print(site, start_date, end_date, device)
        try:
            formatted_data = []
            pump_status = get_all_pump_mode_status(
                site, start_date, end_date, device)
            for log in pump_status:
                mac_address = get_device_mac_address(log[3])
                device = get_device_name_by_mac_address(mac_address)
                site = get_site_name_by_siteid(log[1])
                pump = get_pump_name_by_device_id(log[3])
                formatted_data.append({
                    'site': site[0]['Name'] if site else None,
                    'mac_address': mac_address,
                    'device': device,
                    'pump': pump,
                    'pump_status': "Automation" if log[2] == 1 else "Manual",
                    'date_time': log[4]
                })
            return Response({
                'data': formatted_data,
                'errors': '',
                'code': 200,
                'status': 'success'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'data': None,
                'errors': 'Error retrieving pump status',
                'code': 500,
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
