#!/usr/bin/env python3
"""
Test script for Smart Pump WebSocket functionality
This script can be used to test WebSocket connections and data flow
"""

import asyncio
import websockets
import json
import sys
import os
import django
from datetime import datetime

# Add the project root directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'atg_web.settings.development')
django.setup()

# Now import Django modules
from backend.smart_pump.consumers import push_nozzle_update, push_sales_update
from backend.smart_pump.tasks import push_smart_pump_websocket_updates


class SmartPumpWebSocketTester:
    def __init__(self, site_id, websocket_url=None):
        self.site_id = site_id
        self.websocket_url = websocket_url or f"ws://localhost:8000/ws/smart-pump/{site_id}/"
        self.received_messages = []

    async def test_websocket_connection(self):
        """Test basic WebSocket connection"""
        try:
            print(f"Attempting to connect to: {self.websocket_url}")
            async with websockets.connect(self.websocket_url) as websocket:
                print("✅ WebSocket connection established successfully!")
                
                # Wait for initial messages
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"📨 Received initial message: {message}")
                    self.received_messages.append(json.loads(message))
                except asyncio.TimeoutError:
                    print("⏰ No initial message received within 5 seconds")
                
                return True
        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            return False

    async def test_websocket_with_data_push(self):
        """Test WebSocket connection with data push"""
        try:
            print(f"Connecting to: {self.websocket_url}")
            async with websockets.connect(self.websocket_url) as websocket:
                print("✅ WebSocket connected, pushing test data...")
                
                # Push test nozzle data
                test_nozzle_data = {
                    "data": [
                        {
                            "pump_id": 1,
                            "nozzle_name": "Test Nozzle 1",
                            "product": "PMS",
                            "status": "Online",
                            "last_transaction": datetime.now().isoformat()
                        }
                    ],
                    "currency_symbol": "₦"
                }
                
                # Push test sales data
                test_sales_data = {
                    "pms_total_sales": "1,234.567",
                    "pms_total_volume": "987.654",
                    "ago_total_sales": "2,345.678",
                    "ago_total_volume": "1,876.543",
                    "currency_symbol": "₦"
                }
                
                # Push data using the consumer functions
                push_nozzle_update(self.site_id, test_nozzle_data)
                push_sales_update(self.site_id, test_sales_data)
                
                print("📤 Test data pushed, waiting for messages...")
                
                # Wait for messages
                message_count = 0
                while message_count < 2:  # Expect 2 messages (nozzle + sales)
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        print(f"📨 Received message {message_count + 1}: {message}")
                        self.received_messages.append(json.loads(message))
                        message_count += 1
                    except asyncio.TimeoutError:
                        print(f"⏰ Timeout waiting for message {message_count + 1}")
                        break
                
                return len(self.received_messages) > 0
                
        except Exception as e:
            print(f"❌ WebSocket test with data push failed: {e}")
            return False

    def test_celery_task(self):
        """Test the Celery task for WebSocket updates"""
        try:
            print(f"🔄 Testing Celery task for site {self.site_id}...")
            result = push_smart_pump_websocket_updates.delay(self.site_id)
            print(f"✅ Celery task submitted with ID: {result.id}")
            
            # Wait for task completion (optional)
            try:
                task_result = result.get(timeout=30)
                print(f"📋 Task result: {task_result}")
                return True
            except Exception as e:
                print(f"⚠️ Task execution error: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Celery task test failed: {e}")
            return False

    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*50)
        print("TEST SUMMARY")
        print("="*50)
        print(f"Site ID: {self.site_id}")
        print(f"WebSocket URL: {self.websocket_url}")
        print(f"Messages received: {len(self.received_messages)}")
        
        for i, msg in enumerate(self.received_messages):
            print(f"\nMessage {i+1}:")
            print(f"  Type: {msg.get('type', 'unknown')}")
            print(f"  Timestamp: {msg.get('timestamp', 'N/A')}")
            if 'data' in msg:
                print(f"  Data keys: {list(msg['data'].keys())}")


async def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("Usage: python test_websocket.py <site_id> [websocket_url]")
        print("Example: python test_websocket.py 123")
        sys.exit(1)
    
    site_id = sys.argv[1]
    websocket_url = sys.argv[2] if len(sys.argv) > 2 else None
    
    tester = SmartPumpWebSocketTester(site_id, websocket_url)
    
    print(f"🚀 Starting Smart Pump WebSocket tests for site {site_id}")
    print("-" * 50)
    
    # Test 1: Basic connection
    print("\n1️⃣ Testing basic WebSocket connection...")
    connection_success = await tester.test_websocket_connection()
    
    # Test 2: Connection with data push
    print("\n2️⃣ Testing WebSocket with data push...")
    data_push_success = await tester.test_websocket_with_data_push()
    
    # Test 3: Celery task
    print("\n3️⃣ Testing Celery task...")
    celery_success = tester.test_celery_task()
    
    # Print summary
    tester.print_summary()
    
    # Overall result
    print(f"\n🎯 Overall test result:")
    print(f"  Connection: {'✅' if connection_success else '❌'}")
    print(f"  Data Push: {'✅' if data_push_success else '❌'}")
    print(f"  Celery Task: {'✅' if celery_success else '❌'}")


if __name__ == "__main__":
    asyncio.run(main())
