from django.core.management.base import BaseCommand
from celery import current_app
from backend.smart_pump.tasks import test_redis_connection, push_smart_pump_websocket_updates, push_latest_transactions_websocket
from backend.smart_pump.cache import RedisClient
from datetime import datetime


class Command(BaseCommand):
    help = 'Test Celery functionality for Smart Pump WebSocket tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--site-id',
            type=str,
            default='123',
            help='Site ID to test WebSocket updates (default: 123)'
        )

    def handle(self, *args, **options):
        site_id = options['site_id']

        self.stdout.write("🚀 Starting Celery and Smart Pump Tests")
        self.stdout.write("=" * 50)

        # Test results
        results = {
            'celery_connection': self.test_celery_connection(),
            'redis_direct': self.test_redis_direct(),
            'redis_task': self.test_redis_connection_task(),
            'websocket_task': self.test_websocket_task(site_id),
            'transaction_task': self.test_transaction_task(site_id)
        }

        # Summary
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("📊 TEST SUMMARY")
        self.stdout.write("=" * 50)

        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            self.stdout.write(
                f"{test_name.replace('_', ' ').title()}: {status}")

        all_passed = all(results.values())
        overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
        self.stdout.write(f"\nOverall Status: {overall_status}")

        if not all_passed:
            self.stdout.write("\n🔧 Troubleshooting Tips:")
            if not results['celery_connection']:
                self.stdout.write(
                    "   - Start Celery worker: celery -A atg_web worker --loglevel=info")
            if not results['redis_direct']:
                self.stdout.write("   - Check Redis server: redis-cli ping")
                self.stdout.write(
                    "   - Verify Redis configuration in settings")
            if not results['redis_task'] and results['celery_connection']:
                self.stdout.write("   - Check task registration and imports")
            if not results['websocket_task']:
                self.stdout.write("   - Verify site data exists in database")
                self.stdout.write("   - Check smart pump access permissions")
            if not results['transaction_task']:
                self.stdout.write(
                    "   - Check if transaction data exists for the site")
                self.stdout.write(
                    "   - Verify WebSocket consumer is properly configured")

    def test_celery_connection(self):
        """Test basic Celery connection"""
        self.stdout.write("\n🔍 Testing Celery Connection...")

        try:
            inspect = current_app.control.inspect()

            # Check if workers are available
            stats = inspect.stats()
            if stats:
                self.stdout.write("✅ Celery workers are running")
                for worker, stat in stats.items():
                    self.stdout.write(f"   Worker: {worker}")
                    pool_info = stat.get('pool', {}).get(
                        'max-concurrency', 'N/A')
                    self.stdout.write(f"   Pool: {pool_info} processes")
            else:
                self.stdout.write("❌ No Celery workers found")
                return False

            # Check registered tasks
            registered = inspect.registered()
            if registered:
                self.stdout.write("✅ Tasks are registered")
                for worker, tasks in registered.items():
                    smart_pump_tasks = [
                        task for task in tasks if 'smart_pump' in task]
                    self.stdout.write(
                        f"   Smart Pump tasks on {worker}: {len(smart_pump_tasks)}")
            else:
                self.stdout.write("❌ No registered tasks found")

            return True

        except Exception as e:
            self.stdout.write(f"❌ Celery connection test failed: {e}")
            return False

    def test_redis_connection_task(self):
        """Test Redis connection task"""
        self.stdout.write("\n🔍 Testing Redis Connection Task...")

        try:
            # Submit task
            result = test_redis_connection.delay()
            self.stdout.write(f"✅ Task submitted with ID: {result.id}")

            # Wait for result
            task_result = result.get(timeout=30)
            self.stdout.write("✅ Task completed successfully")
            self.stdout.write(f"   Result: {task_result}")

            return task_result.get('connection_successful', False)

        except Exception as e:
            self.stdout.write(f"❌ Redis connection task failed: {e}")
            return False

    def test_websocket_task(self, site_id="123"):
        """Test WebSocket update task"""
        self.stdout.write(f"\n🔍 Testing WebSocket Task for site {site_id}...")

        try:
            # Submit task
            result = push_smart_pump_websocket_updates.delay(site_id)
            self.stdout.write(
                f"✅ WebSocket task submitted with ID: {result.id}")

            # Wait for result
            task_result = result.get(timeout=60)
            self.stdout.write("✅ WebSocket task completed")
            self.stdout.write(
                f"   Success: {task_result.get('success', False)}")
            self.stdout.write(
                f"   Nozzle count: {task_result.get('nozzle_count', 0)}")

            return task_result.get('success', False)

        except Exception as e:
            self.stdout.write(f"❌ WebSocket task failed: {e}")
            return False

    def test_redis_direct(self):
        """Test Redis connection directly"""
        self.stdout.write("\n🔍 Testing Direct Redis Connection...")

        try:
            redis_client = RedisClient()
            health_status = redis_client.health_check()

            if health_status.get('connected', False):
                self.stdout.write("✅ Redis connection successful")
                info = health_status.get('info', {})
                self.stdout.write(f"   Version: {info.get('version', 'N/A')}")
                self.stdout.write(f"   Clients: {info.get('clients', 'N/A')}")
                return True
            else:
                self.stdout.write("❌ Redis connection failed")
                self.stdout.write(
                    f"   Error: {health_status.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            self.stdout.write(f"❌ Direct Redis test failed: {e}")
            return False

    def test_transaction_task(self, site_id="123"):
        """Test transaction WebSocket update task"""
        self.stdout.write(
            f"\n🔍 Testing Transaction WebSocket Task for site {site_id}...")

        try:
            # Submit task
            result = push_latest_transactions_websocket.delay(site_id, 5)
            self.stdout.write(
                f"✅ Transaction task submitted with ID: {result.id}")

            # Wait for result
            task_result = result.get(timeout=60)
            self.stdout.write("✅ Transaction task completed")
            self.stdout.write(
                f"   Success: {task_result.get('success', False)}")
            self.stdout.write(
                f"   Transaction count: {task_result.get('transaction_count', 0)}")

            return task_result.get('success', False)

        except Exception as e:
            self.stdout.write(f"❌ Transaction task failed: {e}")
            return False
