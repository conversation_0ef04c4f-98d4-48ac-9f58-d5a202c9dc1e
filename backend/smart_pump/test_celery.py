#!/usr/bin/env python3
"""
Test script to verify <PERSON><PERSON><PERSON> is working with Smart Pump tasks
"""

from backend.smart_pump.cache import RedisClient
from backend.smart_pump.tasks import push_smart_pump_websocket_updates, test_redis_connection
from celery import current_app
import os
import sys
from datetime import datetime

# Add the project root directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# Set up Django environment
# Try to determine the correct settings module
try:
    from decouple import config
    environment = config('ENVIRONMENT', default='local')
    settings_module = f'atg_web.settings.{environment}'
except Exception:
    # Fallback to local if decouple fails
    settings_module = 'atg_web.settings.local'

print(f"Using settings module: {settings_module}")
os.environ.setdefault('DJANGO_SETTINGS_MODULE', settings_module)

try:
    import django
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    print("Available settings modules: local, staging, production")
    print("Try setting ENVIRONMENT variable or check your .env file")
    sys.exit(1)

# Now import Django-related modules after setup


def test_celery_connection():
    """Test basic Celery connection"""
    print("🔍 Testing Celery Connection...")

    try:
        inspect = current_app.control.inspect()

        # Check if workers are available
        stats = inspect.stats()
        if stats:
            print("✅ Celery workers are running")
            for worker, stat in stats.items():
                print(f"   Worker: {worker}")
                print(
                    f"   Pool: {stat.get('pool', {}).get('max-concurrency', 'N/A')} processes")
        else:
            print("❌ No Celery workers found")
            return False

        # Check registered tasks
        registered = inspect.registered()
        if registered:
            print("✅ Tasks are registered")
            for worker, tasks in registered.items():
                smart_pump_tasks = [
                    task for task in tasks if 'smart_pump' in task]
                print(
                    f"   Smart Pump tasks on {worker}: {len(smart_pump_tasks)}")
        else:
            print("❌ No registered tasks found")

        return True

    except Exception as e:
        print(f"❌ Celery connection test failed: {e}")
        return False


def test_redis_connection_task():
    """Test Redis connection task"""
    print("\n🔍 Testing Redis Connection Task...")

    try:
        # Submit task
        result = test_redis_connection.delay()
        print(f"✅ Task submitted with ID: {result.id}")

        # Wait for result
        task_result = result.get(timeout=30)
        print(f"✅ Task completed successfully")
        print(f"   Result: {task_result}")

        return task_result.get('connection_successful', False)

    except Exception as e:
        print(f"❌ Redis connection task failed: {e}")
        return False


def test_websocket_task(site_id="123"):
    """Test WebSocket update task"""
    print(f"\n🔍 Testing WebSocket Task for site {site_id}...")

    try:
        # Submit task
        result = push_smart_pump_websocket_updates.delay(site_id)
        print(f"✅ WebSocket task submitted with ID: {result.id}")

        # Wait for result
        task_result = result.get(timeout=60)
        print(f"✅ WebSocket task completed")
        print(f"   Success: {task_result.get('success', False)}")
        print(f"   Nozzle count: {task_result.get('nozzle_count', 0)}")

        return task_result.get('success', False)

    except Exception as e:
        print(f"❌ WebSocket task failed: {e}")
        return False


def test_redis_direct():
    """Test Redis connection directly"""
    print("\n🔍 Testing Direct Redis Connection...")

    try:
        redis_client = RedisClient()
        health_status = redis_client.health_check()

        if health_status.get('connected', False):
            print("✅ Redis connection successful")
            print(
                f"   Version: {health_status.get('info', {}).get('version', 'N/A')}")
            print(
                f"   Clients: {health_status.get('info', {}).get('clients', 'N/A')}")
            return True
        else:
            print("❌ Redis connection failed")
            print(f"   Error: {health_status.get('error', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"❌ Direct Redis test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Celery and Smart Pump Tests")
    print("=" * 50)

    # Test results
    results = {
        'celery_connection': test_celery_connection(),
        'redis_direct': test_redis_direct(),
        'redis_task': test_redis_connection_task(),
        'websocket_task': test_websocket_task()
    }

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)

    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")

    all_passed = all(results.values())
    overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
    print(f"\nOverall Status: {overall_status}")

    if not all_passed:
        print("\n🔧 Troubleshooting Tips:")
        if not results['celery_connection']:
            print("   - Start Celery worker: celery -A atg_web worker --loglevel=info")
        if not results['redis_direct']:
            print("   - Check Redis server: redis-cli ping")
            print("   - Verify Redis configuration in settings")
        if not results['redis_task'] and results['celery_connection']:
            print("   - Check task registration and imports")
        if not results['websocket_task']:
            print("   - Verify site data exists in database")
            print("   - Check smart pump access permissions")

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
