import logging
from backend.currency.utils import get_currency
from backend.smart_pump.queries import get_ago_total_volume, get_cng_total_volume, get_dpk_total_volume, get_pms_total_volume, get_products_by_site_id
from backend.smart_pump.utils import getAllNozzleDetailsInSite
import redis
import json
from decouple import config
from celery import shared_task
import datetime
from backend.smart_pump import cache
from backend.smart_pump.consumers import push_nozzle_update, push_sales_update
from backend import models

logger = logging.getLogger(__name__)
redis_client = cache.RedisClient()


# @shared_task(name='save_smart_pump_dashboard_data_to_redis')
def save_smart_pump_dashboard_data_to_redis(key):
    today_date = datetime.date.today()
    get_date = today_date
    logger.info(
        f"Starting save_smart_pump_dashboard_data_to_redis for key: {key}")

    try:
        site = f"latest:pump:{key}"
        redis_client.delete(site)
        logger.info(f"Processing site: {site}")

        nozzle_data = getAllNozzleDetailsInSite(site)
        logger.info(f"Retrieved nozzle data: items")

        currency_symbol = get_currency(site)
        data_ = {
            "data": nozzle_data,
            "currency_symbol": currency_symbol
        }

        logger.info(f"Attempting to save data to Redis with key: {site}")
        rs = redis_client.set(site, data_)
        logger.info(f"Redis save result: {rs}")

    except Exception as e:
        logger.error(f"Redis caching error: {e}", exc_info=True)
        return False


@shared_task(name='test_redis_connection')
def test_redis_connection():
    try:
        test_key = "test:redis:connection"
        test_value = {"timestamp": str(datetime.datetime.now())}
        logger.info(f"Testing Redis connection with key: {test_key}")

        # Test direct Redis connection
        direct_result = redis_client.client.set(
            test_key + ":direct",
            "direct_test",
            ex=60
        )
        logger.info(f"Direct Redis set result: {direct_result}")

        # Test through our wrapper
        result = redis_client.set(test_key, test_value)
        logger.info(f"Redis client set result: {result}")

        # Try to retrieve the value
        retrieved = redis_client.get(test_key)
        logger.info(f"Retrieved value: {retrieved}")

        return {
            "set_result": result,
            "retrieved_value": retrieved,
            "direct_result": direct_result,
            "connection_successful": retrieved is not None
        }
    except Exception as e:
        logger.error(f"Redis test error: {e}", exc_info=True)
        return {"error": str(e)}


@shared_task(name='push_smart_pump_websocket_updates')
def push_smart_pump_websocket_updates(site_id):
    """
    Celery task to push real-time WebSocket updates for smart pump data
    This can be called periodically or triggered by data changes
    """
    try:
        logger.info(f"Starting WebSocket updates for site: {site_id}")

        # Get nozzle data
        nozzle_data = getAllNozzleDetailsInSite(site_id)
        currency_symbol = get_currency(site_id)
        nozzle_response = {"data": nozzle_data,
                           "currency_symbol": currency_symbol}

        # Push nozzle updates
        push_nozzle_update(site_id, nozzle_response)
        logger.info(f"Pushed nozzle updates for site: {site_id}")

        # Get sales aggregation data
        today_date = datetime.date.today()
        pms_total_volume = get_pms_total_volume(today_date, site_id)
        cng_total_volume = get_cng_total_volume(today_date, site_id)
        ago_total_volume = get_ago_total_volume(today_date, site_id)
        dpk_total_volume = get_dpk_total_volume(today_date, site_id)
        products = get_products_by_site_id(site_id)

        ago_sales = any(x['Name'] == 'AGO' for x in products)
        pms_sales = any(x['Name'] == 'PMS' for x in products)
        cng_sales = any(x['Name'] == 'CNG' for x in products)
        dpk_sales = any(x['Name'] == 'DPK' for x in products)

        sales_data = {
            "pms_total_sales": "{:,.3f}".format(pms_total_volume[0][1]),
            "pms_total_volume": "{:,.3f}".format(pms_total_volume[0][0]),
            "ago_total_sales": "{:,.3f}".format(ago_total_volume[0][1]),
            "ago_total_volume": "{:,.3f}".format(ago_total_volume[0][0]),
            "dpk_total_sales": "{:,.3f}".format(dpk_total_volume[0][1]),
            "dpk_total_volume": "{:,.3f}".format(dpk_total_volume[0][0]),
            "cng_total_sales": "{:,.3f}".format(cng_total_volume[0][1]),
            "cng_total_volume": "{:,.3f}".format(cng_total_volume[0][0]),
            "ago_sales": ago_sales,
            "pms_sales": pms_sales,
            "cng_sales": cng_sales,
            "dpk_sales": dpk_sales,
            "currency_symbol": currency_symbol
        }

        # Push sales updates
        push_sales_update(site_id, sales_data)
        logger.info(f"Pushed sales updates for site: {site_id}")

        return {
            "success": True,
            "site_id": site_id,
            "nozzle_count": len(nozzle_data),
            "timestamp": datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(
            f"WebSocket update error for site {site_id}: {e}", exc_info=True)
        return {"error": str(e), "site_id": site_id}


@shared_task(name='push_smart_pump_websocket_updates_all_sites')
def push_smart_pump_websocket_updates_all_sites():
    """
    Celery task to push WebSocket updates for all sites with smart pump access
    """
    try:
        # Get all sites with smart pump access
        sites_with_smart_pump = models.Sites.objects.filter(
            smartpump_access=1).values_list('Site_id', flat=True)

        logger.info(
            f"Found {len(sites_with_smart_pump)} sites with smart pump access")

        results = []
        for site_id in sites_with_smart_pump:
            try:
                # Call the individual site update task
                result = push_smart_pump_websocket_updates(site_id)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to update site {site_id}: {e}")
                results.append({"error": str(e), "site_id": site_id})

        return {
            "success": True,
            "total_sites": len(sites_with_smart_pump),
            "results": results,
            "timestamp": datetime.datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error updating all sites: {e}", exc_info=True)
        return {"error": str(e)}
