import os
from celery import Celery
from decouple import config
import re

# Fix for Python 3.7+ compatibility
# In Python 3.7+, re._pattern_type was removed
if not hasattr(re, '_pattern_type'):
    re._pattern_type = type(re.compile(''))

environment = config('ENVIRONMENT')
os.environ.setdefault('DJANGO_SETTINGS_MODULE',
                      'atg_web.settings.'+environment)

app = Celery('atg_web')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks()

# Use string-based task routes instead of patterns
app.conf.task_routes = {
    'save_smart_pump_dashboard_data_to_redis': {'queue': 'default'},
    'test_redis_connection': {'queue': 'default'},
    'push_smart_pump_websocket_updates': {'queue': 'default'},
    'push_smart_pump_websocket_updates_all_sites': {'queue': 'default'},
    'push_latest_transactions_websocket': {'queue': 'default'},
}
