import os
from django.core.asgi import get_asgi_application
from channels.routing import Protocol<PERSON>ype<PERSON>outer, URLRouter
from channels.auth import AuthMiddlewareStack
# 👈 your app where we’ll define websocket routes
# Import WebSocket routing from different apps
from backend.tanks import routing as tanks_routing
from backend.smart_pump import routing as smart_pump_routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'atg_web.settings')

# Combine WebSocket URL patterns from different apps
websocket_urlpatterns = (
    tanks_routing.websocket_urlpatterns +
    smart_pump_routing.websocket_urlpatterns
)

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            websocket_urlpatterns
        )
    ),
})
